# Cryptocurrency Exchange Connectivity Analyzer Makefile

.PHONY: help install test run clean setup-system examples

# Default target
help:
	@echo "Cryptocurrency Exchange Connectivity Analyzer"
	@echo "=============================================="
	@echo ""
	@echo "Available targets:"
	@echo "  install       - Install Python dependencies"
	@echo "  setup-system  - Install system dependencies (requires sudo)"
	@echo "  test          - Run test suite"
	@echo "  run           - Run full analysis"
	@echo "  run-major     - Run analysis on major exchanges only"
	@echo "  run-fast      - Run quick analysis with relaxed settings"
	@echo "  examples      - Run example usage scenarios"
	@echo "  clean         - Clean up generated files"
	@echo "  help          - Show this help message"

# Install Python dependencies
install:
	@echo "Installing Python dependencies..."
	pip install -r requirements.txt
	@echo "✓ Dependencies installed"

# Install system dependencies (Linux/macOS)
setup-system:
	@echo "Installing system dependencies..."
	@if command -v apt-get >/dev/null 2>&1; then \
		echo "Detected Debian/Ubuntu system"; \
		sudo apt-get update; \
		sudo apt-get install -y traceroute dnsutils mtr-tiny; \
	elif command -v yum >/dev/null 2>&1; then \
		echo "Detected CentOS/RHEL system"; \
		sudo yum install -y traceroute bind-utils mtr; \
	elif command -v brew >/dev/null 2>&1; then \
		echo "Detected macOS with Homebrew"; \
		brew install mtr; \
	else \
		echo "⚠ Could not detect package manager. Please install manually:"; \
		echo "  - traceroute"; \
		echo "  - dig/nslookup"; \
		echo "  - mtr (optional)"; \
	fi
	@echo "✓ System dependencies installed"

# Run test suite
test:
	@echo "Running test suite..."
	python test_analyzer.py

# Run full analysis
run:
	@echo "Running full cryptocurrency exchange connectivity analysis..."
	python crypto_connectivity_analyzer.py

# Run analysis on major exchanges only
run-major:
	@echo "Running analysis on major exchanges..."
	python crypto_connectivity_analyzer.py --exchanges binance coinbase kraken okx huobi bybit

# Run quick analysis with relaxed settings
run-fast:
	@echo "Running quick analysis..."
	python crypto_connectivity_analyzer.py --exchanges binance coinbase --max-latency 50 --max-cost 200

# Run example scenarios
examples:
	@echo "Running example usage scenarios..."
	python example_usage.py

# Clean up generated files
clean:
	@echo "Cleaning up generated files..."
	rm -rf results/
	rm -rf test_output/
	rm -f *.log
	rm -rf __pycache__/
	rm -rf *.pyc
	find . -name "*.pyc" -delete
	find . -name "__pycache__" -type d -exec rm -rf {} +
	@echo "✓ Cleanup complete"

# Quick setup (install everything)
setup: setup-system install
	@echo "✓ Setup complete! Run 'make test' to verify installation."

# Development targets
dev-install:
	pip install -r requirements.txt
	pip install pytest black flake8 mypy

lint:
	@echo "Running code linting..."
	flake8 *.py --max-line-length=100 --ignore=E203,W503
	black --check *.py

format:
	@echo "Formatting code..."
	black *.py

# Docker targets (if you want to containerize)
docker-build:
	docker build -t crypto-connectivity-analyzer .

docker-run:
	docker run --rm -v $(PWD)/results:/app/results crypto-connectivity-analyzer

# Show system information
info:
	@echo "System Information:"
	@echo "==================="
	@echo "Python version: $(shell python --version)"
	@echo "Operating system: $(shell uname -s)"
	@echo "Architecture: $(shell uname -m)"
	@echo ""
	@echo "Network tools:"
	@command -v traceroute >/dev/null 2>&1 && echo "✓ traceroute available" || echo "✗ traceroute not found"
	@command -v dig >/dev/null 2>&1 && echo "✓ dig available" || echo "✗ dig not found"
	@command -v nslookup >/dev/null 2>&1 && echo "✓ nslookup available" || echo "✗ nslookup not found"
	@command -v mtr >/dev/null 2>&1 && echo "✓ mtr available" || echo "✗ mtr not found"
	@echo ""
	@echo "Python packages:"
	@python -c "import sys; print('✓ Python', sys.version.split()[0])"
	@python -c "import asyncio; print('✓ asyncio available')" 2>/dev/null || echo "✗ asyncio not available"
	@python -c "import yaml; print('✓ PyYAML available')" 2>/dev/null || echo "✗ PyYAML not available"
	@python -c "import dns.resolver; print('✓ dnspython available')" 2>/dev/null || echo "⚠ dnspython not available"
	@python -c "from ipwhois import IPWhois; print('✓ ipwhois available')" 2>/dev/null || echo "⚠ ipwhois not available"

# Performance test with single exchange
perf-test:
	@echo "Running performance test..."
	time python crypto_connectivity_analyzer.py --exchanges binance --verbose

# Generate documentation
docs:
	@echo "Generating documentation..."
	@echo "# Cryptocurrency Exchange Connectivity Analyzer" > USAGE.md
	@echo "" >> USAGE.md
	@echo "## Quick Start" >> USAGE.md
	@echo "\`\`\`bash" >> USAGE.md
	@echo "make setup    # Install dependencies" >> USAGE.md
	@echo "make test     # Verify installation" >> USAGE.md
	@echo "make run      # Run full analysis" >> USAGE.md
	@echo "\`\`\`" >> USAGE.md
	@echo "✓ Documentation generated in USAGE.md"

# Show recent results
results:
	@echo "Recent analysis results:"
	@echo "========================"
	@if [ -d "results" ]; then \
		ls -la results/ | head -10; \
	else \
		echo "No results directory found. Run 'make run' first."; \
	fi

# Backup results
backup:
	@echo "Backing up results..."
	@if [ -d "results" ]; then \
		tar -czf results_backup_$(shell date +%Y%m%d_%H%M%S).tar.gz results/; \
		echo "✓ Results backed up"; \
	else \
		echo "No results to backup"; \
	fi
