{"metadata": {"generated_at": "2025-05-28T20:00:46.967719", "version": "1.0", "total_exchanges_analyzed": 20, "total_recommendations": 15}, "analysis_results": [{"url": "wss://stream.binance.com:9443/ws", "hostname": "stream.binance.com", "dns": {"hostname": "stream.binance.com", "ipv4_addresses": ["*************", "************", "***********", "*************", "**************", "*************", "************", "*************"], "ipv6_addresses": [], "cname": null, "ttl": 59, "error": null}, "ping_results": [{"target": "*************", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": ""}, {"target": "************", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": ""}, {"target": "***********", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": ""}], "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "***********", "target_ip": "***********", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "*************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "*********/13", "country": null, "city": null, "organization": "Amazon Data Services Japan", "isp": null, "error": null}, {"ip_address": "************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/14", "country": null, "city": null, "organization": "Amazon Data Services Japan", "isp": null, "error": null}, {"ip_address": "***********", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/16", "country": null, "city": null, "organization": "Amazon Data Services Japan", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Google Cloud Platform", "type": "cloud", "asn_ranges": ["AS15169", "AS36040"], "ip_ranges": ["********/8", "********/8", "***********/16"], "datacenters": [{"name": "us-central1", "provider": "GCP", "location": "Iowa", "city": "Council Bluffs", "country": "US", "region": "North America", "facility_code": null, "address": null, "connectivity_options": []}, {"name": "us-east1", "provider": "GCP", "location": "S. Carolina", "city": "Moncks Corner", "country": "US", "region": "North America", "facility_code": null, "address": null, "connectivity_options": []}, {"name": "europe-west1", "provider": "GCP", "location": "Belgium", "city": "<PERSON><PERSON>", "country": "BE", "region": "Europe", "facility_code": null, "address": null, "connectivity_options": []}, {"name": "asia-southeast1", "provider": "GCP", "location": "Singapore", "city": "Singapore", "country": "SG", "region": "Asia Pacific", "facility_code": null, "address": null, "connectivity_options": []}], "website": "https://cloud.google.com"}, "datacenter": null, "connectivity_options": [{"type": "Compute Engine", "description": "VM instance in nearest GCP region", "latency_estimate": "< 5ms", "cost_estimate": "$40-400/month", "setup_time": "Minutes"}], "confidence": "medium"}}, {"url": "wss://fstream.binance.com/ws", "hostname": "fstream.binance.com", "dns": {"hostname": "fstream.binance.com", "ipv4_addresses": ["************", "*************", "***********", "************", "**************", "************", "*************", "************"], "ipv6_addresses": [], "cname": null, "ttl": 59, "error": null}, "ping_results": [{"target": "************", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": ""}, {"target": "*************", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": ""}, {"target": "***********", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": ""}], "traceroute_results": [{"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "***********", "target_ip": "***********", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/15", "country": null, "city": null, "organization": "Amazon Data Services Japan", "isp": null, "error": null}, {"ip_address": "*************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/14", "country": null, "city": null, "organization": "Amazon Data Services Japan", "isp": null, "error": null}, {"ip_address": "***********", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "*********/13", "country": null, "city": null, "organization": "Amazon Data Services Japan", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Amazon Web Services", "type": "cloud", "asn_ranges": ["AS16509", "AS14618"], "ip_ranges": ["********/8", "********/8", "*******/8"], "datacenters": [{"name": "us-east-1", "provider": "AWS", "location": "N. Virginia", "city": "<PERSON><PERSON>", "country": "US", "region": "North America", "facility_code": "IAD", "address": null, "connectivity_options": []}, {"name": "us-west-2", "provider": "AWS", "location": "Oregon", "city": "Portland", "country": "US", "region": "North America", "facility_code": "PDX", "address": null, "connectivity_options": []}, {"name": "eu-west-1", "provider": "AWS", "location": "Ireland", "city": "Dublin", "country": "IE", "region": "Europe", "facility_code": "DUB", "address": null, "connectivity_options": []}, {"name": "ap-southeast-1", "provider": "AWS", "location": "Singapore", "city": "Singapore", "country": "SG", "region": "Asia Pacific", "facility_code": "SIN", "address": null, "connectivity_options": []}, {"name": "ap-northeast-1", "provider": "AWS", "location": "Tokyo", "city": "Tokyo", "country": "JP", "region": "Asia Pacific", "facility_code": "NRT", "address": null, "connectivity_options": []}], "website": "https://aws.amazon.com"}, "datacenter": null, "connectivity_options": [{"type": "EC2 Instance", "description": "EC2 instance in nearest AWS region", "latency_estimate": "< 5ms", "cost_estimate": "$50-500/month", "setup_time": "Minutes"}, {"type": "Direct Connect", "description": "Dedicated connection to AWS", "latency_estimate": "< 2ms", "cost_estimate": "$500-5000/month", "setup_time": "Weeks"}], "confidence": "medium"}}, {"url": "wss://stream.binance.us:9443/ws", "hostname": "stream.binance.us", "dns": {"hostname": "stream.binance.us", "ipv4_addresses": ["*************", "************"], "ipv6_addresses": [], "cname": "usa-prod-mbx-pubwsp-alb-291245639.us-east-1.elb.amazonaws.com.", "ttl": 59, "error": null}, "ping_results": [{"target": "*************", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": ""}, {"target": "************", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": ""}], "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "*************", "asn": "14618", "asn_description": "AMAZON-AES, US", "network": "*********/12", "country": null, "city": null, "organization": "Amazon Data Services NoVa", "isp": null, "error": null}, {"ip_address": "************", "asn": "14618", "asn_description": "AMAZON-AES, US", "network": "*********/12", "country": null, "city": null, "organization": "Amazon Data Services NoVa", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Amazon Web Services", "type": "cloud", "asn_ranges": ["AS16509", "AS14618"], "ip_ranges": ["********/8", "********/8", "*******/8"], "datacenters": [{"name": "us-east-1", "provider": "AWS", "location": "N. Virginia", "city": "<PERSON><PERSON>", "country": "US", "region": "North America", "facility_code": "IAD", "address": null, "connectivity_options": []}, {"name": "us-west-2", "provider": "AWS", "location": "Oregon", "city": "Portland", "country": "US", "region": "North America", "facility_code": "PDX", "address": null, "connectivity_options": []}, {"name": "eu-west-1", "provider": "AWS", "location": "Ireland", "city": "Dublin", "country": "IE", "region": "Europe", "facility_code": "DUB", "address": null, "connectivity_options": []}, {"name": "ap-southeast-1", "provider": "AWS", "location": "Singapore", "city": "Singapore", "country": "SG", "region": "Asia Pacific", "facility_code": "SIN", "address": null, "connectivity_options": []}, {"name": "ap-northeast-1", "provider": "AWS", "location": "Tokyo", "city": "Tokyo", "country": "JP", "region": "Asia Pacific", "facility_code": "NRT", "address": null, "connectivity_options": []}], "website": "https://aws.amazon.com"}, "datacenter": null, "connectivity_options": [{"type": "EC2 Instance", "description": "EC2 instance in nearest AWS region", "latency_estimate": "< 5ms", "cost_estimate": "$50-500/month", "setup_time": "Minutes"}, {"type": "Direct Connect", "description": "Dedicated connection to AWS", "latency_estimate": "< 2ms", "cost_estimate": "$500-5000/month", "setup_time": "Weeks"}], "confidence": "medium"}}, {"url": "wss://ws-feed.exchange.coinbase.com", "hostname": "ws-feed.exchange.coinbase.com", "dns": {"hostname": "ws-feed.exchange.coinbase.com", "ipv4_addresses": ["*************", "*************"], "ipv6_addresses": ["2606:4700:4400::ac40:974e", "2606:4700:4400::6812:24b2"], "cname": null, "ttl": 299, "error": null}, "ping_results": [{"target": "*************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.611, "avg_rtt_ms": 11.5, "max_rtt_ms": 14.243, "success": true, "error": null}, {"target": "*************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.199, "avg_rtt_ms": 10.459, "max_rtt_ms": 10.973, "success": true, "error": null}, {"target": "2606:4700:4400::ac40:974e", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": "ping6: connect: Network is unreachable"}], "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "2606:4700:4400::ac40:974e", "target_ip": "2606:4700:4400::ac40:974e", "hops": [], "total_hops": 0, "success": false, "error": "connect: Network is unreachable"}], "whois_results": [{"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/13", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "2606:4700:4400::ac40:974e", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "2606:4700::/32", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Cloudflare", "type": "cdn", "asn_ranges": ["AS13335"], "ip_ranges": ["**********/12", "**********/13"], "datacenters": [], "website": "https://www.cloudflare.com"}, "datacenter": null, "connectivity_options": [{"type": "Cloudflare Workers", "description": "Edge computing near Cloudflare PoP", "latency_estimate": "< 10ms", "cost_estimate": "$5-50/month", "setup_time": "Minutes"}, {"type": "VPS near PoP", "description": "VPS in same city as Cloudflare PoP", "latency_estimate": "< 15ms", "cost_estimate": "$20-200/month", "setup_time": "Hours"}], "confidence": "medium"}}, {"url": "wss://ws.kraken.com", "hostname": "ws.kraken.com", "dns": {"hostname": "ws.kraken.com", "ipv4_addresses": ["**************", "**************", "**************", "**************", "**************"], "ipv6_addresses": [], "cname": null, "ttl": 299, "error": null}, "ping_results": [{"target": "**************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.263, "avg_rtt_ms": 10.406, "max_rtt_ms": 10.5, "success": true, "error": null}, {"target": "**************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.804, "avg_rtt_ms": 11.051, "max_rtt_ms": 11.378, "success": true, "error": null}, {"target": "**************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 11.044, "avg_rtt_ms": 11.211, "max_rtt_ms": 11.492, "success": true, "error": null}], "traceroute_results": [{"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "**************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "**************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "**************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Cloudflare", "type": "cdn", "asn_ranges": ["AS13335"], "ip_ranges": ["**********/12", "**********/13"], "datacenters": [], "website": "https://www.cloudflare.com"}, "datacenter": null, "connectivity_options": [{"type": "Cloudflare Workers", "description": "Edge computing near Cloudflare PoP", "latency_estimate": "< 10ms", "cost_estimate": "$5-50/month", "setup_time": "Minutes"}, {"type": "VPS near PoP", "description": "VPS in same city as Cloudflare PoP", "latency_estimate": "< 15ms", "cost_estimate": "$20-200/month", "setup_time": "Hours"}], "confidence": "medium"}}, {"url": "wss://futures.kraken.com/ws/v1", "hostname": "futures.kraken.com", "dns": {"hostname": "futures.kraken.com", "ipv4_addresses": ["**************", "************"], "ipv6_addresses": [], "cname": null, "ttl": 299, "error": null}, "ping_results": [{"target": "**************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.948, "avg_rtt_ms": 11.66, "max_rtt_ms": 13.013, "success": true, "error": null}, {"target": "************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.397, "avg_rtt_ms": 10.53, "max_rtt_ms": 10.619, "success": true, "error": null}], "traceroute_results": [{"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "**************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/13", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Cloudflare", "type": "cdn", "asn_ranges": ["AS13335"], "ip_ranges": ["**********/12", "**********/13"], "datacenters": [], "website": "https://www.cloudflare.com"}, "datacenter": null, "connectivity_options": [{"type": "Cloudflare Workers", "description": "Edge computing near Cloudflare PoP", "latency_estimate": "< 10ms", "cost_estimate": "$5-50/month", "setup_time": "Minutes"}, {"type": "VPS near PoP", "description": "VPS in same city as Cloudflare PoP", "latency_estimate": "< 15ms", "cost_estimate": "$20-200/month", "setup_time": "Hours"}], "confidence": "medium"}}, {"url": "wss://ws.okx.com:8443/ws/v5/public", "hostname": "ws.okx.com", "dns": {"hostname": "ws.okx.com", "ipv4_addresses": ["*************", "*************"], "ipv6_addresses": ["2606:4700:4400::ac40:9052", "2606:4700:4400::6812:2bae"], "cname": "ws.okx.com.cdn.cloudflare.net.", "ttl": 299, "error": null}, "ping_results": [{"target": "*************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 11.659, "avg_rtt_ms": 11.744, "max_rtt_ms": 11.793, "success": true, "error": null}, {"target": "*************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 11.094, "avg_rtt_ms": 11.239, "max_rtt_ms": 11.337, "success": true, "error": null}, {"target": "2606:4700:4400::ac40:9052", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": "ping6: connect: Network is unreachable"}], "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "2606:4700:4400::ac40:9052", "target_ip": "2606:4700:4400::ac40:9052", "hops": [], "total_hops": 0, "success": false, "error": "connect: Network is unreachable"}], "whois_results": [{"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/13", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "2606:4700:4400::ac40:9052", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "2606:4700::/32", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Cloudflare", "type": "cdn", "asn_ranges": ["AS13335"], "ip_ranges": ["**********/12", "**********/13"], "datacenters": [], "website": "https://www.cloudflare.com"}, "datacenter": null, "connectivity_options": [{"type": "Cloudflare Workers", "description": "Edge computing near Cloudflare PoP", "latency_estimate": "< 10ms", "cost_estimate": "$5-50/month", "setup_time": "Minutes"}, {"type": "VPS near PoP", "description": "VPS in same city as Cloudflare PoP", "latency_estimate": "< 15ms", "cost_estimate": "$20-200/month", "setup_time": "Hours"}], "confidence": "medium"}}, {"url": "wss://api.huobi.pro/ws", "hostname": "api.huobi.pro", "dns": {"hostname": "api.huobi.pro", "ipv4_addresses": ["**************", "***************", "**************", "*************"], "ipv6_addresses": ["2600:9000:233d:5c00:14:c251:2440:93a1", "2600:9000:233d:3e00:14:c251:2440:93a1", "2600:9000:233d:5600:14:c251:2440:93a1", "2600:9000:233d:9a00:14:c251:2440:93a1", "2600:9000:233d:ce00:14:c251:2440:93a1", "2600:9000:233d:a600:14:c251:2440:93a1", "2600:9000:233d:8800:14:c251:2440:93a1", "2600:9000:233d:8000:14:c251:2440:93a1"], "cname": "d3b78lpogv2hld.cloudfront.net.", "ttl": 59, "error": null}, "ping_results": [{"target": "**************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.207, "avg_rtt_ms": 11.205, "max_rtt_ms": 14.396, "success": true, "error": null}, {"target": "***************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.327, "avg_rtt_ms": 10.838, "max_rtt_ms": 12.264, "success": true, "error": null}, {"target": "**************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.658, "avg_rtt_ms": 11.418, "max_rtt_ms": 13.017, "success": true, "error": null}], "traceroute_results": [{"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "***************", "target_ip": "***************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "**************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "***********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "***************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "***********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "**************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "***********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": null, "datacenter": null, "connectivity_options": [], "confidence": "low"}}, {"url": "wss://api.hbdm.com/ws", "hostname": "api.hbdm.com", "dns": {"hostname": "api.hbdm.com", "ipv4_addresses": ["**************", "**************"], "ipv6_addresses": ["2606:4700::6811:a7bb", "2606:4700::6811:a8bb"], "cname": "api.hbdm.com.cdn.cloudflare.net.", "ttl": 299, "error": null}, "ping_results": [{"target": "**************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 11.832, "avg_rtt_ms": 12.08, "max_rtt_ms": 12.468, "success": true, "error": null}, {"target": "**************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.425, "avg_rtt_ms": 10.604, "max_rtt_ms": 10.971, "success": true, "error": null}, {"target": "2606:4700::6811:a7bb", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": "ping6: connect: Network is unreachable"}], "traceroute_results": [{"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "2606:4700::6811:a7bb", "target_ip": "2606:4700::6811:a7bb", "hops": [], "total_hops": 0, "success": false, "error": "connect: Network is unreachable"}], "whois_results": [{"ip_address": "**************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "**************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "2606:4700::6811:a7bb", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "2606:4700::/32", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Cloudflare", "type": "cdn", "asn_ranges": ["AS13335"], "ip_ranges": ["**********/12", "**********/13"], "datacenters": [], "website": "https://www.cloudflare.com"}, "datacenter": null, "connectivity_options": [{"type": "Cloudflare Workers", "description": "Edge computing near Cloudflare PoP", "latency_estimate": "< 10ms", "cost_estimate": "$5-50/month", "setup_time": "Minutes"}, {"type": "VPS near PoP", "description": "VPS in same city as Cloudflare PoP", "latency_estimate": "< 15ms", "cost_estimate": "$20-200/month", "setup_time": "Hours"}], "confidence": "medium"}}, {"url": "wss://stream.bybit.com/v5/public/spot", "hostname": "stream.bybit.com", "dns": {"hostname": "stream.bybit.com", "ipv4_addresses": ["************", "************", "************", "************"], "ipv6_addresses": ["2600:9000:24d1:400:17:57ca:2ec0:93a1", "2600:9000:24d1:3400:17:57ca:2ec0:93a1", "2600:9000:24d1:9600:17:57ca:2ec0:93a1", "2600:9000:24d1:ce00:17:57ca:2ec0:93a1", "2600:9000:24d1:8c00:17:57ca:2ec0:93a1", "2600:9000:24d1:aa00:17:57ca:2ec0:93a1", "2600:9000:24d1:6600:17:57ca:2ec0:93a1", "2600:9000:24d1:c200:17:57ca:2ec0:93a1"], "cname": "d2mo22rbksh9yz.cloudfront.net.", "ttl": 59, "error": null}, "ping_results": [{"target": "************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.778, "avg_rtt_ms": 12.981, "max_rtt_ms": 21.506, "success": true, "error": null}, {"target": "************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 11.432, "avg_rtt_ms": 11.964, "max_rtt_ms": 13.835, "success": true, "error": null}, {"target": "************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 11.333, "avg_rtt_ms": 11.929, "max_rtt_ms": 13.793, "success": true, "error": null}], "traceroute_results": [{"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "*********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "*********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "*********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": null, "datacenter": null, "connectivity_options": [], "confidence": "low"}}, {"url": "wss://stream.bybit.com/v5/public/linear", "hostname": "stream.bybit.com", "dns": {"hostname": "stream.bybit.com", "ipv4_addresses": ["************", "************", "************", "************"], "ipv6_addresses": ["2600:9000:2358:5c00:17:57ca:2ec0:93a1", "2600:9000:2358:e000:17:57ca:2ec0:93a1", "2600:9000:2358:c000:17:57ca:2ec0:93a1", "2600:9000:2358:2e00:17:57ca:2ec0:93a1", "2600:9000:2358:e400:17:57ca:2ec0:93a1", "2600:9000:2358:fc00:17:57ca:2ec0:93a1", "2600:9000:2358:8400:17:57ca:2ec0:93a1", "2600:9000:2358:2200:17:57ca:2ec0:93a1"], "cname": "d2mo22rbksh9yz.cloudfront.net.", "ttl": 50, "error": null}, "ping_results": [{"target": "************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.867, "avg_rtt_ms": 11.559, "max_rtt_ms": 13.923, "success": true, "error": null}, {"target": "************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.702, "avg_rtt_ms": 11.529, "max_rtt_ms": 14.668, "success": true, "error": null}, {"target": "************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 11.23, "avg_rtt_ms": 11.395, "max_rtt_ms": 11.632, "success": true, "error": null}], "traceroute_results": [{"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "*********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "*********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "*********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": null, "datacenter": null, "connectivity_options": [], "confidence": "low"}}, {"url": "wss://ws-api.kucoin.com/endpoint", "hostname": "ws-api.kucoin.com", "dns": {"hostname": "ws-api.kucoin.com", "ipv4_addresses": ["*************", "*************", "*************", "************"], "ipv6_addresses": ["2600:9000:2358:9400:2:bc38:d480:93a1", "2600:9000:2358:e800:2:bc38:d480:93a1", "2600:9000:2358:f400:2:bc38:d480:93a1", "2600:9000:2358:ec00:2:bc38:d480:93a1", "2600:9000:2358:7e00:2:bc38:d480:93a1", "2600:9000:2358:d200:2:bc38:d480:93a1", "2600:9000:2358:4000:2:bc38:d480:93a1", "2600:9000:2358:ba00:2:bc38:d480:93a1"], "cname": "d2rtq1fxrp1hea.cloudfront.net.", "ttl": 59, "error": null}, "ping_results": [{"target": "*************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.304, "avg_rtt_ms": 10.754, "max_rtt_ms": 11.878, "success": true, "error": null}, {"target": "*************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.415, "avg_rtt_ms": 11.724, "max_rtt_ms": 16.323, "success": true, "error": null}, {"target": "*************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.316, "avg_rtt_ms": 10.968, "max_rtt_ms": 13.161, "success": true, "error": null}], "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "*************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "***********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "***********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "***********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": null, "datacenter": null, "connectivity_options": [], "confidence": "low"}}, {"url": "wss://api.gateio.ws/ws/v4/", "hostname": "api.gateio.ws", "dns": {"hostname": "api.gateio.ws", "ipv4_addresses": ["**************", "***********", "************", "************", "*********", "*************", "***********", "**************"], "ipv6_addresses": [], "cname": "dualstack.balancer-gateio-ws-66098844.ap-northeast-1.elb.amazonaws.com.", "ttl": 59, "error": null}, "ping_results": [{"target": "**************", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": ""}, {"target": "***********", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": ""}, {"target": "************", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": ""}], "traceroute_results": [{"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "***********", "target_ip": "***********", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "**************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/12, **********/11, **********/12, **********/13, **********/14, **********/15", "country": null, "city": null, "organization": "Amazon Technologies Inc.", "isp": null, "error": null}, {"ip_address": "***********", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/16", "country": null, "city": null, "organization": "Amazon Data Services Japan", "isp": null, "error": null}, {"ip_address": "************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/15", "country": null, "city": null, "organization": "Amazon Data Services Japan", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Amazon Web Services", "type": "cloud", "asn_ranges": ["AS16509", "AS14618"], "ip_ranges": ["********/8", "********/8", "*******/8"], "datacenters": [{"name": "us-east-1", "provider": "AWS", "location": "N. Virginia", "city": "<PERSON><PERSON>", "country": "US", "region": "North America", "facility_code": "IAD", "address": null, "connectivity_options": []}, {"name": "us-west-2", "provider": "AWS", "location": "Oregon", "city": "Portland", "country": "US", "region": "North America", "facility_code": "PDX", "address": null, "connectivity_options": []}, {"name": "eu-west-1", "provider": "AWS", "location": "Ireland", "city": "Dublin", "country": "IE", "region": "Europe", "facility_code": "DUB", "address": null, "connectivity_options": []}, {"name": "ap-southeast-1", "provider": "AWS", "location": "Singapore", "city": "Singapore", "country": "SG", "region": "Asia Pacific", "facility_code": "SIN", "address": null, "connectivity_options": []}, {"name": "ap-northeast-1", "provider": "AWS", "location": "Tokyo", "city": "Tokyo", "country": "JP", "region": "Asia Pacific", "facility_code": "NRT", "address": null, "connectivity_options": []}], "website": "https://aws.amazon.com"}, "datacenter": null, "connectivity_options": [{"type": "EC2 Instance", "description": "EC2 instance in nearest AWS region", "latency_estimate": "< 5ms", "cost_estimate": "$50-500/month", "setup_time": "Minutes"}, {"type": "Direct Connect", "description": "Dedicated connection to AWS", "latency_estimate": "< 2ms", "cost_estimate": "$500-5000/month", "setup_time": "Weeks"}], "confidence": "medium"}}, {"url": "wss://api-pub.bitfinex.com/ws/2", "hostname": "api-pub.bitfinex.com", "dns": {"hostname": "api-pub.bitfinex.com", "ipv4_addresses": ["*************", "*************", "*************", "*************", "*************"], "ipv6_addresses": [], "cname": null, "ttl": 299, "error": null}, "ping_results": [{"target": "*************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.273, "avg_rtt_ms": 10.389, "max_rtt_ms": 10.506, "success": true, "error": null}, {"target": "*************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.5, "avg_rtt_ms": 10.656, "max_rtt_ms": 11.073, "success": true, "error": null}, {"target": "*************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.983, "avg_rtt_ms": 11.179, "max_rtt_ms": 11.366, "success": true, "error": null}], "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Cloudflare", "type": "cdn", "asn_ranges": ["AS13335"], "ip_ranges": ["**********/12", "**********/13"], "datacenters": [], "website": "https://www.cloudflare.com"}, "datacenter": null, "connectivity_options": [{"type": "Cloudflare Workers", "description": "Edge computing near Cloudflare PoP", "latency_estimate": "< 10ms", "cost_estimate": "$5-50/month", "setup_time": "Minutes"}, {"type": "VPS near PoP", "description": "VPS in same city as Cloudflare PoP", "latency_estimate": "< 15ms", "cost_estimate": "$20-200/month", "setup_time": "Hours"}], "confidence": "medium"}}, {"url": "wss://ws.bitstamp.net", "hostname": "ws.bitstamp.net", "dns": {"hostname": "ws.bitstamp.net", "ipv4_addresses": ["*************", "************", "**************", "*************", "***********", "**************"], "ipv6_addresses": [], "cname": "websocket-**********.eu-central-1.elb.amazonaws.com.", "ttl": 59, "error": null}, "ping_results": [{"target": "*************", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": ""}, {"target": "************", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": ""}, {"target": "**************", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": ""}], "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "*************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/15", "country": null, "city": null, "organization": "A100 ROW GmbH", "isp": null, "error": null}, {"ip_address": "************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "*********/15", "country": null, "city": null, "organization": "A100 ROW GmbH", "isp": null, "error": null}, {"ip_address": "**************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/14", "country": null, "city": null, "organization": "A100 ROW GmbH", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Amazon Web Services", "type": "cloud", "asn_ranges": ["AS16509", "AS14618"], "ip_ranges": ["********/8", "********/8", "*******/8"], "datacenters": [{"name": "us-east-1", "provider": "AWS", "location": "N. Virginia", "city": "<PERSON><PERSON>", "country": "US", "region": "North America", "facility_code": "IAD", "address": null, "connectivity_options": []}, {"name": "us-west-2", "provider": "AWS", "location": "Oregon", "city": "Portland", "country": "US", "region": "North America", "facility_code": "PDX", "address": null, "connectivity_options": []}, {"name": "eu-west-1", "provider": "AWS", "location": "Ireland", "city": "Dublin", "country": "IE", "region": "Europe", "facility_code": "DUB", "address": null, "connectivity_options": []}, {"name": "ap-southeast-1", "provider": "AWS", "location": "Singapore", "city": "Singapore", "country": "SG", "region": "Asia Pacific", "facility_code": "SIN", "address": null, "connectivity_options": []}, {"name": "ap-northeast-1", "provider": "AWS", "location": "Tokyo", "city": "Tokyo", "country": "JP", "region": "Asia Pacific", "facility_code": "NRT", "address": null, "connectivity_options": []}], "website": "https://aws.amazon.com"}, "datacenter": null, "connectivity_options": [{"type": "EC2 Instance", "description": "EC2 instance in nearest AWS region", "latency_estimate": "< 5ms", "cost_estimate": "$50-500/month", "setup_time": "Minutes"}, {"type": "Direct Connect", "description": "Dedicated connection to AWS", "latency_estimate": "< 2ms", "cost_estimate": "$500-5000/month", "setup_time": "Weeks"}], "confidence": "medium"}}, {"url": "wss://api.gemini.com/v1/marketdata", "hostname": "api.gemini.com", "dns": {"hostname": "api.gemini.com", "ipv4_addresses": ["*************", "*************", "************", "************", "**************", "*************"], "ipv6_addresses": [], "cname": "prod-dc-alb-backup-**********.us-east-1.elb.amazonaws.com.", "ttl": 59, "error": null}, "ping_results": [{"target": "*************", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": ""}, {"target": "*************", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": ""}, {"target": "************", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": ""}], "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "*************", "asn": "14618", "asn_description": "AMAZON-AES, US", "network": "**********/12, **********/11, **********/12, **********/13, **********/14, **********/15", "country": null, "city": null, "organization": "Amazon Technologies Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "14618", "asn_description": "AMAZON-AES, US", "network": "*********/13", "country": null, "city": null, "organization": "Amazon Data Services NoVa", "isp": null, "error": null}, {"ip_address": "************", "asn": "14618", "asn_description": "AMAZON-AES, US", "network": "********/10, *********/12", "country": null, "city": null, "organization": "Amazon Technologies Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Amazon Web Services", "type": "cloud", "asn_ranges": ["AS16509", "AS14618"], "ip_ranges": ["********/8", "********/8", "*******/8"], "datacenters": [{"name": "us-east-1", "provider": "AWS", "location": "N. Virginia", "city": "<PERSON><PERSON>", "country": "US", "region": "North America", "facility_code": "IAD", "address": null, "connectivity_options": []}, {"name": "us-west-2", "provider": "AWS", "location": "Oregon", "city": "Portland", "country": "US", "region": "North America", "facility_code": "PDX", "address": null, "connectivity_options": []}, {"name": "eu-west-1", "provider": "AWS", "location": "Ireland", "city": "Dublin", "country": "IE", "region": "Europe", "facility_code": "DUB", "address": null, "connectivity_options": []}, {"name": "ap-southeast-1", "provider": "AWS", "location": "Singapore", "city": "Singapore", "country": "SG", "region": "Asia Pacific", "facility_code": "SIN", "address": null, "connectivity_options": []}, {"name": "ap-northeast-1", "provider": "AWS", "location": "Tokyo", "city": "Tokyo", "country": "JP", "region": "Asia Pacific", "facility_code": "NRT", "address": null, "connectivity_options": []}], "website": "https://aws.amazon.com"}, "datacenter": null, "connectivity_options": [{"type": "EC2 Instance", "description": "EC2 instance in nearest AWS region", "latency_estimate": "< 5ms", "cost_estimate": "$50-500/month", "setup_time": "Minutes"}, {"type": "Direct Connect", "description": "Dedicated connection to AWS", "latency_estimate": "< 2ms", "cost_estimate": "$500-5000/month", "setup_time": "Weeks"}], "confidence": "medium"}}, {"url": "wss://www.deribit.com/ws/api/v2", "hostname": "www.deribit.com", "dns": {"hostname": "www.deribit.com", "ipv4_addresses": ["*************"], "ipv6_addresses": [], "cname": null, "ttl": 29, "error": null}, "ping_results": [{"target": "*************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 96.122, "avg_rtt_ms": 97.176, "max_rtt_ms": 100.951, "success": true, "error": null}], "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "*************", "asn": "207856", "asn_description": "DERIBIT, NL", "network": "***********/24", "country": "GB", "city": null, "organization": "mnt-nl-deribit-1", "isp": null, "error": null}], "hosting_infrastructure": {"provider": null, "datacenter": null, "connectivity_options": [], "confidence": "low"}}, {"url": "wss://www.bitmex.com/realtime", "hostname": "www.bitmex.com", "dns": {"hostname": "www.bitmex.com", "ipv4_addresses": ["*************", "*************"], "ipv6_addresses": [], "cname": "www.bitmex.com.cdn.cloudflare.net.", "ttl": 59, "error": null}, "ping_results": [{"target": "*************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.983, "avg_rtt_ms": 11.443, "max_rtt_ms": 12.674, "success": true, "error": null}, {"target": "*************", "packets_sent": 5, "packets_received": 4, "packet_loss_percent": 20.0, "min_rtt_ms": 11.069, "avg_rtt_ms": 11.689, "max_rtt_ms": 13.136, "success": true, "error": null}], "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/13", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Cloudflare", "type": "cdn", "asn_ranges": ["AS13335"], "ip_ranges": ["**********/12", "**********/13"], "datacenters": [], "website": "https://www.cloudflare.com"}, "datacenter": null, "connectivity_options": [{"type": "Cloudflare Workers", "description": "Edge computing near Cloudflare PoP", "latency_estimate": "< 10ms", "cost_estimate": "$5-50/month", "setup_time": "Minutes"}, {"type": "VPS near PoP", "description": "VPS in same city as Cloudflare PoP", "latency_estimate": "< 15ms", "cost_estimate": "$20-200/month", "setup_time": "Hours"}], "confidence": "medium"}}, {"url": "wss://stream.crypto.com/v2/market", "hostname": "stream.crypto.com", "dns": {"hostname": "stream.crypto.com", "ipv4_addresses": ["*************", "*************"], "ipv6_addresses": ["2606:4700::6813:df11", "2606:4700::6813:de11"], "cname": null, "ttl": 299, "error": null}, "ping_results": [{"target": "*************", "packets_sent": 5, "packets_received": 4, "packet_loss_percent": 20.0, "min_rtt_ms": 11.039, "avg_rtt_ms": 11.068, "max_rtt_ms": 11.093, "success": true, "error": null}, {"target": "*************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.984, "avg_rtt_ms": 11.074, "max_rtt_ms": 11.245, "success": true, "error": null}, {"target": "2606:4700::6813:df11", "packets_sent": 5, "packets_received": 0, "packet_loss_percent": 100.0, "min_rtt_ms": 0.0, "avg_rtt_ms": 0.0, "max_rtt_ms": 0.0, "success": false, "error": "ping6: connect: Network is unreachable"}], "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "2606:4700::6813:df11", "target_ip": "2606:4700::6813:df11", "hops": [], "total_hops": 0, "success": false, "error": "connect: Network is unreachable"}], "whois_results": [{"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "2606:4700::6813:df11", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "2606:4700::/32", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Cloudflare", "type": "cdn", "asn_ranges": ["AS13335"], "ip_ranges": ["**********/12", "**********/13"], "datacenters": [], "website": "https://www.cloudflare.com"}, "datacenter": null, "connectivity_options": [{"type": "Cloudflare Workers", "description": "Edge computing near Cloudflare PoP", "latency_estimate": "< 10ms", "cost_estimate": "$5-50/month", "setup_time": "Minutes"}, {"type": "VPS near PoP", "description": "VPS in same city as Cloudflare PoP", "latency_estimate": "< 15ms", "cost_estimate": "$20-200/month", "setup_time": "Hours"}], "confidence": "medium"}}, {"url": "wss://ftx.com/ws/", "hostname": "ftx.com", "dns": {"hostname": "ftx.com", "ipv4_addresses": ["**********", "***********", "************", "***********"], "ipv6_addresses": [], "cname": null, "ttl": 59, "error": null}, "ping_results": [{"target": "**********", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.258, "avg_rtt_ms": 10.843, "max_rtt_ms": 12.503, "success": true, "error": null}, {"target": "***********", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.223, "avg_rtt_ms": 11.398, "max_rtt_ms": 14.981, "success": true, "error": null}, {"target": "************", "packets_sent": 5, "packets_received": 5, "packet_loss_percent": 0.0, "min_rtt_ms": 10.304, "avg_rtt_ms": 11.952, "max_rtt_ms": 17.535, "success": true, "error": null}], "traceroute_results": [{"target": "**********", "target_ip": "**********", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "***********", "target_ip": "***********", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "**********", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "*********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "***********", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "*********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "*********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Amazon Web Services", "type": "cloud", "asn_ranges": ["AS16509", "AS14618"], "ip_ranges": ["********/8", "********/8", "*******/8"], "datacenters": [{"name": "us-east-1", "provider": "AWS", "location": "N. Virginia", "city": "<PERSON><PERSON>", "country": "US", "region": "North America", "facility_code": "IAD", "address": null, "connectivity_options": []}, {"name": "us-west-2", "provider": "AWS", "location": "Oregon", "city": "Portland", "country": "US", "region": "North America", "facility_code": "PDX", "address": null, "connectivity_options": []}, {"name": "eu-west-1", "provider": "AWS", "location": "Ireland", "city": "Dublin", "country": "IE", "region": "Europe", "facility_code": "DUB", "address": null, "connectivity_options": []}, {"name": "ap-southeast-1", "provider": "AWS", "location": "Singapore", "city": "Singapore", "country": "SG", "region": "Asia Pacific", "facility_code": "SIN", "address": null, "connectivity_options": []}, {"name": "ap-northeast-1", "provider": "AWS", "location": "Tokyo", "city": "Tokyo", "country": "JP", "region": "Asia Pacific", "facility_code": "NRT", "address": null, "connectivity_options": []}], "website": "https://aws.amazon.com"}, "datacenter": null, "connectivity_options": [{"type": "EC2 Instance", "description": "EC2 instance in nearest AWS region", "latency_estimate": "< 5ms", "cost_estimate": "$50-500/month", "setup_time": "Minutes"}, {"type": "Direct Connect", "description": "Dedicated connection to AWS", "latency_estimate": "< 2ms", "cost_estimate": "$500-5000/month", "setup_time": "Weeks"}], "confidence": "medium"}}], "recommendations": [{"exchange_name": "Binance", "provider": "Amazon Web Services", "datacenter": "Unknown", "connection_type": "EC2 Instance", "estimated_latency": "< 5ms", "monthly_cost_range": "$50-500/month", "setup_time": "Minutes", "confidence_score": 0.****************, "pros": ["Very low latency (< 5ms)", "Quick setup time", "Scalable and flexible", "Managed infrastructure"], "cons": [], "next_steps": ["Create account with cloud provider", "Select appropriate instance size and location", "Deploy and configure trading infrastructure", "Test connectivity and performance", "Conduct thorough latency and performance testing", "Implement monitoring and alerting"]}, {"exchange_name": "Binance", "provider": "Amazon Web Services", "datacenter": "Unknown", "connection_type": "EC2 Instance", "estimated_latency": "< 5ms", "monthly_cost_range": "$50-500/month", "setup_time": "Minutes", "confidence_score": 0.****************, "pros": ["Very low latency (< 5ms)", "Quick setup time", "Scalable and flexible", "Managed infrastructure"], "cons": [], "next_steps": ["Create account with cloud provider", "Select appropriate instance size and location", "Deploy and configure trading infrastructure", "Test connectivity and performance", "Conduct thorough latency and performance testing", "Implement monitoring and alerting"]}, {"exchange_name": "Gate.io", "provider": "Amazon Web Services", "datacenter": "Unknown", "connection_type": "EC2 Instance", "estimated_latency": "< 5ms", "monthly_cost_range": "$50-500/month", "setup_time": "Minutes", "confidence_score": 0.****************, "pros": ["Very low latency (< 5ms)", "Quick setup time", "Scalable and flexible", "Managed infrastructure"], "cons": [], "next_steps": ["Create account with cloud provider", "Select appropriate instance size and location", "Deploy and configure trading infrastructure", "Test connectivity and performance", "Conduct thorough latency and performance testing", "Implement monitoring and alerting"]}, {"exchange_name": "Bitstamp", "provider": "Amazon Web Services", "datacenter": "Unknown", "connection_type": "EC2 Instance", "estimated_latency": "< 5ms", "monthly_cost_range": "$50-500/month", "setup_time": "Minutes", "confidence_score": 0.****************, "pros": ["Very low latency (< 5ms)", "Quick setup time", "Scalable and flexible", "Managed infrastructure"], "cons": [], "next_steps": ["Create account with cloud provider", "Select appropriate instance size and location", "Deploy and configure trading infrastructure", "Test connectivity and performance", "Conduct thorough latency and performance testing", "Implement monitoring and alerting"]}, {"exchange_name": "Gemini", "provider": "Amazon Web Services", "datacenter": "Unknown", "connection_type": "EC2 Instance", "estimated_latency": "< 5ms", "monthly_cost_range": "$50-500/month", "setup_time": "Minutes", "confidence_score": 0.****************, "pros": ["Very low latency (< 5ms)", "Quick setup time", "Scalable and flexible", "Managed infrastructure"], "cons": [], "next_steps": ["Create account with cloud provider", "Select appropriate instance size and location", "Deploy and configure trading infrastructure", "Test connectivity and performance", "Conduct thorough latency and performance testing", "Implement monitoring and alerting"]}, {"exchange_name": "Binance", "provider": "Google Cloud Platform", "datacenter": "Unknown", "connection_type": "Compute Engine", "estimated_latency": "< 5ms", "monthly_cost_range": "$40-400/month", "setup_time": "Minutes", "confidence_score": 0.874, "pros": ["Very low latency (< 5ms)", "Quick setup time"], "cons": [], "next_steps": ["Conduct thorough latency and performance testing", "Implement monitoring and alerting"]}, {"exchange_name": "Coinbase", "provider": "Cloudflare", "datacenter": "Unknown", "connection_type": "Cloudflare Workers", "estimated_latency": "< 10ms", "monthly_cost_range": "$5-50/month", "setup_time": "Minutes", "confidence_score": 0.53175, "pros": ["Quick setup time", "Cost-effective solution", "Scalable and flexible", "Managed infrastructure"], "cons": [], "next_steps": ["Create account with cloud provider", "Select appropriate instance size and location", "Deploy and configure trading infrastructure", "Test connectivity and performance", "Conduct thorough latency and performance testing", "Implement monitoring and alerting"]}, {"exchange_name": "<PERSON><PERSON><PERSON>", "provider": "Cloudflare", "datacenter": "Unknown", "connection_type": "Cloudflare Workers", "estimated_latency": "< 10ms", "monthly_cost_range": "$5-50/month", "setup_time": "Minutes", "confidence_score": 0.53175, "pros": ["Quick setup time", "Cost-effective solution", "Scalable and flexible", "Managed infrastructure"], "cons": [], "next_steps": ["Create account with cloud provider", "Select appropriate instance size and location", "Deploy and configure trading infrastructure", "Test connectivity and performance", "Conduct thorough latency and performance testing", "Implement monitoring and alerting"]}, {"exchange_name": "<PERSON><PERSON><PERSON>", "provider": "Cloudflare", "datacenter": "Unknown", "connection_type": "Cloudflare Workers", "estimated_latency": "< 10ms", "monthly_cost_range": "$5-50/month", "setup_time": "Minutes", "confidence_score": 0.53175, "pros": ["Quick setup time", "Cost-effective solution", "Scalable and flexible", "Managed infrastructure"], "cons": [], "next_steps": ["Create account with cloud provider", "Select appropriate instance size and location", "Deploy and configure trading infrastructure", "Test connectivity and performance", "Conduct thorough latency and performance testing", "Implement monitoring and alerting"]}, {"exchange_name": "OKX", "provider": "Cloudflare", "datacenter": "Unknown", "connection_type": "Cloudflare Workers", "estimated_latency": "< 10ms", "monthly_cost_range": "$5-50/month", "setup_time": "Minutes", "confidence_score": 0.53175, "pros": ["Quick setup time", "Cost-effective solution", "Scalable and flexible", "Managed infrastructure"], "cons": [], "next_steps": ["Create account with cloud provider", "Select appropriate instance size and location", "Deploy and configure trading infrastructure", "Test connectivity and performance", "Conduct thorough latency and performance testing", "Implement monitoring and alerting"]}, {"exchange_name": "Api", "provider": "Cloudflare", "datacenter": "Unknown", "connection_type": "Cloudflare Workers", "estimated_latency": "< 10ms", "monthly_cost_range": "$5-50/month", "setup_time": "Minutes", "confidence_score": 0.53175, "pros": ["Quick setup time", "Cost-effective solution", "Scalable and flexible", "Managed infrastructure"], "cons": [], "next_steps": ["Create account with cloud provider", "Select appropriate instance size and location", "Deploy and configure trading infrastructure", "Test connectivity and performance", "Conduct thorough latency and performance testing", "Implement monitoring and alerting"]}, {"exchange_name": "Bitfinex", "provider": "Cloudflare", "datacenter": "Unknown", "connection_type": "Cloudflare Workers", "estimated_latency": "< 10ms", "monthly_cost_range": "$5-50/month", "setup_time": "Minutes", "confidence_score": 0.53175, "pros": ["Quick setup time", "Cost-effective solution", "Scalable and flexible", "Managed infrastructure"], "cons": [], "next_steps": ["Create account with cloud provider", "Select appropriate instance size and location", "Deploy and configure trading infrastructure", "Test connectivity and performance", "Conduct thorough latency and performance testing", "Implement monitoring and alerting"]}, {"exchange_name": "BitMEX", "provider": "Cloudflare", "datacenter": "Unknown", "connection_type": "Cloudflare Workers", "estimated_latency": "< 10ms", "monthly_cost_range": "$5-50/month", "setup_time": "Minutes", "confidence_score": 0.53175, "pros": ["Quick setup time", "Cost-effective solution", "Scalable and flexible", "Managed infrastructure"], "cons": [], "next_steps": ["Create account with cloud provider", "Select appropriate instance size and location", "Deploy and configure trading infrastructure", "Test connectivity and performance", "Conduct thorough latency and performance testing", "Implement monitoring and alerting"]}, {"exchange_name": "Crypto.com", "provider": "Cloudflare", "datacenter": "Unknown", "connection_type": "Cloudflare Workers", "estimated_latency": "< 10ms", "monthly_cost_range": "$5-50/month", "setup_time": "Minutes", "confidence_score": 0.53175, "pros": ["Quick setup time", "Cost-effective solution", "Scalable and flexible", "Managed infrastructure"], "cons": [], "next_steps": ["Create account with cloud provider", "Select appropriate instance size and location", "Deploy and configure trading infrastructure", "Test connectivity and performance", "Conduct thorough latency and performance testing", "Implement monitoring and alerting"]}, {"exchange_name": "FTX", "provider": "Amazon Web Services", "datacenter": "Unknown", "connection_type": "EC2 Instance", "estimated_latency": "< 5ms", "monthly_cost_range": "$50-500/month", "setup_time": "Minutes", "confidence_score": 0.*****************, "pros": ["Quick setup time", "Scalable and flexible", "Managed infrastructure"], "cons": [], "next_steps": ["Create account with cloud provider", "Select appropriate instance size and location", "Deploy and configure trading infrastructure", "Test connectivity and performance", "Conduct thorough latency and performance testing", "Implement monitoring and alerting"]}], "summary": {"total_exchanges": 20, "successful_analyses": 20, "failed_analyses": 0, "avg_latency": 13.***************, "best_latency": 10.389, "worst_latency": 97.176, "provider_distribution": {"Google Cloud Platform": 1, "Amazon Web Services": 6, "Cloudflare": 8, "Unknown": 5}, "unique_providers": 4}}