{"metadata": {"generated_at": "2025-05-28T17:50:31.714603", "version": "1.0", "total_exchanges_analyzed": 20, "total_recommendations": 0}, "analysis_results": [{"url": "wss://stream.binance.com:9443/ws", "hostname": "stream.binance.com", "dns": {"hostname": "stream.binance.com", "ipv4_addresses": ["***********", "************", "************", "*************", "**************", "**************", "**************", "**************"], "ipv6_addresses": [], "cname": null, "ttl": 59, "error": null}, "traceroute_results": [{"target": "***********", "target_ip": "***********", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": false, "error": "Traceroute timeout after 30 seconds"}], "whois_results": [{"ip_address": "***********", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/15", "country": null, "city": null, "organization": "Amazon Data Services Japan", "isp": null, "error": null}, {"ip_address": "************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "*********/13", "country": null, "city": null, "organization": "Amazon Data Services Japan", "isp": null, "error": null}, {"ip_address": "************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/13", "country": "US", "city": null, "organization": "AMAZON ASIA-PACIFIC RESOURCES PRIVATE LIMITED - ne", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Google Cloud Platform", "type": "cloud", "asn_ranges": ["AS15169", "AS36040"], "ip_ranges": ["********/8", "********/8", "***********/16"], "datacenters": [{"name": "us-central1", "provider": "GCP", "location": "Iowa", "city": "Council Bluffs", "country": "US", "region": "North America", "facility_code": null, "address": null, "connectivity_options": []}, {"name": "us-east1", "provider": "GCP", "location": "S. Carolina", "city": "Moncks Corner", "country": "US", "region": "North America", "facility_code": null, "address": null, "connectivity_options": []}, {"name": "europe-west1", "provider": "GCP", "location": "Belgium", "city": "<PERSON><PERSON>", "country": "BE", "region": "Europe", "facility_code": null, "address": null, "connectivity_options": []}, {"name": "asia-southeast1", "provider": "GCP", "location": "Singapore", "city": "Singapore", "country": "SG", "region": "Asia Pacific", "facility_code": null, "address": null, "connectivity_options": []}], "website": "https://cloud.google.com"}, "datacenter": null, "connectivity_options": [], "confidence": "medium"}}, {"url": "wss://fstream.binance.com/ws", "hostname": "fstream.binance.com", "dns": {"hostname": "fstream.binance.com", "ipv4_addresses": ["*************", "*************", "**************", "************", "*************", "**************", "**************", "************"], "ipv6_addresses": [], "cname": null, "ttl": 59, "error": null}, "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": false, "error": "Traceroute timeout after 30 seconds"}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": false, "error": "Traceroute timeout after 30 seconds"}, {"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "*************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/16", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/14", "country": null, "city": null, "organization": "Amazon Data Services Japan", "isp": null, "error": null}, {"ip_address": "**************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/14", "country": null, "city": null, "organization": "Amazon Data Services Japan", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Amazon Web Services", "type": "cloud", "asn_ranges": ["AS16509", "AS14618"], "ip_ranges": ["********/8", "********/8", "*******/8"], "datacenters": [{"name": "us-east-1", "provider": "AWS", "location": "N. Virginia", "city": "<PERSON><PERSON>", "country": "US", "region": "North America", "facility_code": "IAD", "address": null, "connectivity_options": []}, {"name": "us-west-2", "provider": "AWS", "location": "Oregon", "city": "Portland", "country": "US", "region": "North America", "facility_code": "PDX", "address": null, "connectivity_options": []}, {"name": "eu-west-1", "provider": "AWS", "location": "Ireland", "city": "Dublin", "country": "IE", "region": "Europe", "facility_code": "DUB", "address": null, "connectivity_options": []}, {"name": "ap-southeast-1", "provider": "AWS", "location": "Singapore", "city": "Singapore", "country": "SG", "region": "Asia Pacific", "facility_code": "SIN", "address": null, "connectivity_options": []}, {"name": "ap-northeast-1", "provider": "AWS", "location": "Tokyo", "city": "Tokyo", "country": "JP", "region": "Asia Pacific", "facility_code": "NRT", "address": null, "connectivity_options": []}], "website": "https://aws.amazon.com"}, "datacenter": null, "connectivity_options": [], "confidence": "medium"}}, {"url": "wss://stream.binance.us:9443/ws", "hostname": "stream.binance.us", "dns": {"hostname": "stream.binance.us", "ipv4_addresses": ["*************", "************"], "ipv6_addresses": [], "cname": "usa-prod-mbx-pubwsp-alb-291245639.us-east-1.elb.amazonaws.com.", "ttl": 59, "error": null}, "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": false, "error": "Traceroute timeout after 30 seconds"}, {"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": false, "error": "Traceroute timeout after 30 seconds"}], "whois_results": [{"ip_address": "*************", "asn": "14618", "asn_description": "AMAZON-AES, US", "network": "*********/12", "country": null, "city": null, "organization": "Amazon Data Services NoVa", "isp": null, "error": null}, {"ip_address": "************", "asn": "14618", "asn_description": "AMAZON-AES, US", "network": "*********/12", "country": null, "city": null, "organization": "Amazon Data Services NoVa", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Amazon Web Services", "type": "cloud", "asn_ranges": ["AS16509", "AS14618"], "ip_ranges": ["********/8", "********/8", "*******/8"], "datacenters": [{"name": "us-east-1", "provider": "AWS", "location": "N. Virginia", "city": "<PERSON><PERSON>", "country": "US", "region": "North America", "facility_code": "IAD", "address": null, "connectivity_options": []}, {"name": "us-west-2", "provider": "AWS", "location": "Oregon", "city": "Portland", "country": "US", "region": "North America", "facility_code": "PDX", "address": null, "connectivity_options": []}, {"name": "eu-west-1", "provider": "AWS", "location": "Ireland", "city": "Dublin", "country": "IE", "region": "Europe", "facility_code": "DUB", "address": null, "connectivity_options": []}, {"name": "ap-southeast-1", "provider": "AWS", "location": "Singapore", "city": "Singapore", "country": "SG", "region": "Asia Pacific", "facility_code": "SIN", "address": null, "connectivity_options": []}, {"name": "ap-northeast-1", "provider": "AWS", "location": "Tokyo", "city": "Tokyo", "country": "JP", "region": "Asia Pacific", "facility_code": "NRT", "address": null, "connectivity_options": []}], "website": "https://aws.amazon.com"}, "datacenter": null, "connectivity_options": [], "confidence": "medium"}}, {"url": "wss://ws-feed.exchange.coinbase.com", "hostname": "ws-feed.exchange.coinbase.com", "dns": {"hostname": "ws-feed.exchange.coinbase.com", "ipv4_addresses": ["*************", "*************"], "ipv6_addresses": ["2606:4700:4400::6812:24b2", "2606:4700:4400::ac40:974e"], "cname": null, "ttl": 299, "error": null}, "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "2606:4700:4400::6812:24b2", "target_ip": "2606:4700:4400::6812:24b2", "hops": [], "total_hops": 0, "success": false, "error": "connect: Network is unreachable"}], "whois_results": [{"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/13", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "2606:4700:4400::6812:24b2", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "2606:4700::/32", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Cloudflare", "type": "cdn", "asn_ranges": ["AS13335"], "ip_ranges": ["**********/12", "**********/13"], "datacenters": [], "website": "https://www.cloudflare.com"}, "datacenter": null, "connectivity_options": [], "confidence": "medium"}}, {"url": "wss://ws.kraken.com", "hostname": "ws.kraken.com", "dns": {"hostname": "ws.kraken.com", "ipv4_addresses": ["**************", "**************", "**************", "**************", "**************"], "ipv6_addresses": [], "cname": null, "ttl": 299, "error": null}, "traceroute_results": [{"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "**************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "**************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "**************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Cloudflare", "type": "cdn", "asn_ranges": ["AS13335"], "ip_ranges": ["**********/12", "**********/13"], "datacenters": [], "website": "https://www.cloudflare.com"}, "datacenter": null, "connectivity_options": [], "confidence": "medium"}}, {"url": "wss://futures.kraken.com/ws/v1", "hostname": "futures.kraken.com", "dns": {"hostname": "futures.kraken.com", "ipv4_addresses": ["**************", "************"], "ipv6_addresses": [], "cname": null, "ttl": 299, "error": null}, "traceroute_results": [{"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "**************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/13", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Cloudflare", "type": "cdn", "asn_ranges": ["AS13335"], "ip_ranges": ["**********/12", "**********/13"], "datacenters": [], "website": "https://www.cloudflare.com"}, "datacenter": null, "connectivity_options": [], "confidence": "medium"}}, {"url": "wss://ws.okx.com:8443/ws/v5/public", "hostname": "ws.okx.com", "dns": {"hostname": "ws.okx.com", "ipv4_addresses": ["*************", "*************"], "ipv6_addresses": ["2606:4700:4400::ac40:9052", "2606:4700:4400::6812:2bae"], "cname": "ws.okx.com.cdn.cloudflare.net.", "ttl": 299, "error": null}, "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "2606:4700:4400::ac40:9052", "target_ip": "2606:4700:4400::ac40:9052", "hops": [], "total_hops": 0, "success": false, "error": "connect: Network is unreachable"}], "whois_results": [{"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/13", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "2606:4700:4400::ac40:9052", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "2606:4700::/32", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Cloudflare", "type": "cdn", "asn_ranges": ["AS13335"], "ip_ranges": ["**********/12", "**********/13"], "datacenters": [], "website": "https://www.cloudflare.com"}, "datacenter": null, "connectivity_options": [], "confidence": "medium"}}, {"url": "wss://api.huobi.pro/ws", "hostname": "api.huobi.pro", "dns": {"hostname": "api.huobi.pro", "ipv4_addresses": ["**************", "**************", "***************", "*************"], "ipv6_addresses": ["2600:9000:24f8:e000:14:c251:2440:93a1", "2600:9000:24f8:5600:14:c251:2440:93a1", "2600:9000:24f8:5000:14:c251:2440:93a1", "2600:9000:24f8:fe00:14:c251:2440:93a1", "2600:9000:24f8:1800:14:c251:2440:93a1", "2600:9000:24f8:a000:14:c251:2440:93a1", "2600:9000:24f8:3a00:14:c251:2440:93a1", "2600:9000:24f8:e200:14:c251:2440:93a1"], "cname": "d3b78lpogv2hld.cloudfront.net.", "ttl": 59, "error": null}, "traceroute_results": [{"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "***************", "target_ip": "***************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "**************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "***********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "**************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "***********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "***************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "***********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": null, "datacenter": null, "connectivity_options": [], "confidence": "low"}}, {"url": "wss://api.hbdm.com/ws", "hostname": "api.hbdm.com", "dns": {"hostname": "api.hbdm.com", "ipv4_addresses": ["**************", "**************"], "ipv6_addresses": ["2606:4700::6811:a8bb", "2606:4700::6811:a7bb"], "cname": "api.hbdm.com.cdn.cloudflare.net.", "ttl": 299, "error": null}, "traceroute_results": [{"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "2606:4700::6811:a8bb", "target_ip": "2606:4700::6811:a8bb", "hops": [], "total_hops": 0, "success": false, "error": "connect: Network is unreachable"}], "whois_results": [{"ip_address": "**************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "**************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "2606:4700::6811:a8bb", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "2606:4700::/32", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Cloudflare", "type": "cdn", "asn_ranges": ["AS13335"], "ip_ranges": ["**********/12", "**********/13"], "datacenters": [], "website": "https://www.cloudflare.com"}, "datacenter": null, "connectivity_options": [], "confidence": "medium"}}, {"url": "wss://stream.bybit.com/v5/public/spot", "hostname": "stream.bybit.com", "dns": {"hostname": "stream.bybit.com", "ipv4_addresses": ["************", "************", "************", "************"], "ipv6_addresses": ["2600:9000:24d1:1400:17:57ca:2ec0:93a1", "2600:9000:24d1:9200:17:57ca:2ec0:93a1", "2600:9000:24d1:e00:17:57ca:2ec0:93a1", "2600:9000:24d1:c600:17:57ca:2ec0:93a1", "2600:9000:24d1:5400:17:57ca:2ec0:93a1", "2600:9000:24d1:5200:17:57ca:2ec0:93a1", "2600:9000:24d1:aa00:17:57ca:2ec0:93a1", "2600:9000:24d1:ba00:17:57ca:2ec0:93a1"], "cname": "d2mo22rbksh9yz.cloudfront.net.", "ttl": 59, "error": null}, "traceroute_results": [{"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "*********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "*********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "*********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": null, "datacenter": null, "connectivity_options": [], "confidence": "low"}}, {"url": "wss://stream.bybit.com/v5/public/linear", "hostname": "stream.bybit.com", "dns": {"hostname": "stream.bybit.com", "ipv4_addresses": ["**************", "*************", "**************", "***************"], "ipv6_addresses": ["2600:9000:2358:dc00:17:57ca:2ec0:93a1", "2600:9000:2358:9000:17:57ca:2ec0:93a1", "2600:9000:2358:ca00:17:57ca:2ec0:93a1", "2600:9000:2358:4400:17:57ca:2ec0:93a1", "2600:9000:2358:8600:17:57ca:2ec0:93a1", "2600:9000:2358:4200:17:57ca:2ec0:93a1", "2600:9000:2358:1400:17:57ca:2ec0:93a1", "2600:9000:2358:c000:17:57ca:2ec0:93a1"], "cname": "d2mo22rbksh9yz.cloudfront.net.", "ttl": 53, "error": null}, "traceroute_results": [{"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "**************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "***********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "***********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "**************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "***********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": null, "datacenter": null, "connectivity_options": [], "confidence": "low"}}, {"url": "wss://ws-api.kucoin.com/endpoint", "hostname": "ws-api.kucoin.com", "dns": {"hostname": "ws-api.kucoin.com", "ipv4_addresses": ["*************", "*************", "*************", "************"], "ipv6_addresses": ["2600:9000:2351:8400:2:bc38:d480:93a1", "2600:9000:2351:7400:2:bc38:d480:93a1", "2600:9000:2351:3000:2:bc38:d480:93a1", "2600:9000:2351:4400:2:bc38:d480:93a1", "2600:9000:2351:8a00:2:bc38:d480:93a1", "2600:9000:2351:2200:2:bc38:d480:93a1", "2600:9000:2351:2000:2:bc38:d480:93a1", "2600:9000:2351:3c00:2:bc38:d480:93a1"], "cname": "d2rtq1fxrp1hea.cloudfront.net.", "ttl": 59, "error": null}, "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": false, "error": "Traceroute timeout after 30 seconds"}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "*************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "***********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "***********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "***********/14", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": null, "datacenter": null, "connectivity_options": [], "confidence": "low"}}, {"url": "wss://api.gateio.ws/ws/v4/", "hostname": "api.gateio.ws", "dns": {"hostname": "api.gateio.ws", "ipv4_addresses": ["*************", "**************", "**************", "***********", "************", "*************", "*************", "*************"], "ipv6_addresses": [], "cname": "dualstack.balancer-gateio-ws-66098844.ap-northeast-1.elb.amazonaws.com.", "ttl": 59, "error": null}, "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": false, "error": "Traceroute timeout after 30 seconds"}, {"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "*************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/14", "country": null, "city": null, "organization": "Amazon Data Services Japan", "isp": null, "error": null}, {"ip_address": "**************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/14", "country": null, "city": null, "organization": "Amazon Data Services Japan", "isp": null, "error": null}, {"ip_address": "**************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/16", "country": null, "city": null, "organization": "Amazon Data Services Japan", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Amazon Web Services", "type": "cloud", "asn_ranges": ["AS16509", "AS14618"], "ip_ranges": ["********/8", "********/8", "*******/8"], "datacenters": [{"name": "us-east-1", "provider": "AWS", "location": "N. Virginia", "city": "<PERSON><PERSON>", "country": "US", "region": "North America", "facility_code": "IAD", "address": null, "connectivity_options": []}, {"name": "us-west-2", "provider": "AWS", "location": "Oregon", "city": "Portland", "country": "US", "region": "North America", "facility_code": "PDX", "address": null, "connectivity_options": []}, {"name": "eu-west-1", "provider": "AWS", "location": "Ireland", "city": "Dublin", "country": "IE", "region": "Europe", "facility_code": "DUB", "address": null, "connectivity_options": []}, {"name": "ap-southeast-1", "provider": "AWS", "location": "Singapore", "city": "Singapore", "country": "SG", "region": "Asia Pacific", "facility_code": "SIN", "address": null, "connectivity_options": []}, {"name": "ap-northeast-1", "provider": "AWS", "location": "Tokyo", "city": "Tokyo", "country": "JP", "region": "Asia Pacific", "facility_code": "NRT", "address": null, "connectivity_options": []}], "website": "https://aws.amazon.com"}, "datacenter": null, "connectivity_options": [], "confidence": "medium"}}, {"url": "wss://api-pub.bitfinex.com/ws/2", "hostname": "api-pub.bitfinex.com", "dns": {"hostname": "api-pub.bitfinex.com", "ipv4_addresses": ["*************", "*************", "*************", "*************", "*************"], "ipv6_addresses": [], "cname": null, "ttl": 299, "error": null}, "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Cloudflare", "type": "cdn", "asn_ranges": ["AS13335"], "ip_ranges": ["**********/12", "**********/13"], "datacenters": [], "website": "https://www.cloudflare.com"}, "datacenter": null, "connectivity_options": [], "confidence": "medium"}}, {"url": "wss://ws.bitstamp.net", "hostname": "ws.bitstamp.net", "dns": {"hostname": "ws.bitstamp.net", "ipv4_addresses": ["************", "*************", "**************", "***********", "**************", "*************"], "ipv6_addresses": [], "cname": "websocket-**********.eu-central-1.elb.amazonaws.com.", "ttl": 59, "error": null}, "traceroute_results": [{"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": false, "error": "Traceroute timeout after 30 seconds"}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": false, "error": "Traceroute timeout after 30 seconds"}, {"target": "**************", "target_ip": "**************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "*********/15", "country": null, "city": null, "organization": "A100 ROW GmbH", "isp": null, "error": null}, {"ip_address": "*************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/15", "country": null, "city": null, "organization": "A100 ROW GmbH", "isp": null, "error": null}, {"ip_address": "**************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/14", "country": null, "city": null, "organization": "A100 ROW GmbH", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Amazon Web Services", "type": "cloud", "asn_ranges": ["AS16509", "AS14618"], "ip_ranges": ["********/8", "********/8", "*******/8"], "datacenters": [{"name": "us-east-1", "provider": "AWS", "location": "N. Virginia", "city": "<PERSON><PERSON>", "country": "US", "region": "North America", "facility_code": "IAD", "address": null, "connectivity_options": []}, {"name": "us-west-2", "provider": "AWS", "location": "Oregon", "city": "Portland", "country": "US", "region": "North America", "facility_code": "PDX", "address": null, "connectivity_options": []}, {"name": "eu-west-1", "provider": "AWS", "location": "Ireland", "city": "Dublin", "country": "IE", "region": "Europe", "facility_code": "DUB", "address": null, "connectivity_options": []}, {"name": "ap-southeast-1", "provider": "AWS", "location": "Singapore", "city": "Singapore", "country": "SG", "region": "Asia Pacific", "facility_code": "SIN", "address": null, "connectivity_options": []}, {"name": "ap-northeast-1", "provider": "AWS", "location": "Tokyo", "city": "Tokyo", "country": "JP", "region": "Asia Pacific", "facility_code": "NRT", "address": null, "connectivity_options": []}], "website": "https://aws.amazon.com"}, "datacenter": null, "connectivity_options": [], "confidence": "medium"}}, {"url": "wss://api.gemini.com/v1/marketdata", "hostname": "api.gemini.com", "dns": {"hostname": "api.gemini.com", "ipv4_addresses": ["*************", "*************", "*************", "**************", "************", "************"], "ipv6_addresses": [], "cname": "prod-dc-alb-backup-**********.us-east-1.elb.amazonaws.com.", "ttl": 59, "error": null}, "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": false, "error": "Traceroute timeout after 30 seconds"}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": false, "error": "Traceroute timeout after 30 seconds"}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": false, "error": "Traceroute timeout after 30 seconds"}], "whois_results": [{"ip_address": "*************", "asn": "14618", "asn_description": "AMAZON-AES, US", "network": "**********/12, **********/11, **********/12, **********/13, **********/14, **********/15", "country": null, "city": null, "organization": "Amazon Technologies Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "14618", "asn_description": "AMAZON-AES, US", "network": "**********/11", "country": null, "city": null, "organization": "Amazon Data Services NoVa", "isp": null, "error": null}, {"ip_address": "*************", "asn": "14618", "asn_description": "AMAZON-AES, US", "network": "*********/13", "country": null, "city": null, "organization": "Amazon Data Services NoVa", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Amazon Web Services", "type": "cloud", "asn_ranges": ["AS16509", "AS14618"], "ip_ranges": ["********/8", "********/8", "*******/8"], "datacenters": [{"name": "us-east-1", "provider": "AWS", "location": "N. Virginia", "city": "<PERSON><PERSON>", "country": "US", "region": "North America", "facility_code": "IAD", "address": null, "connectivity_options": []}, {"name": "us-west-2", "provider": "AWS", "location": "Oregon", "city": "Portland", "country": "US", "region": "North America", "facility_code": "PDX", "address": null, "connectivity_options": []}, {"name": "eu-west-1", "provider": "AWS", "location": "Ireland", "city": "Dublin", "country": "IE", "region": "Europe", "facility_code": "DUB", "address": null, "connectivity_options": []}, {"name": "ap-southeast-1", "provider": "AWS", "location": "Singapore", "city": "Singapore", "country": "SG", "region": "Asia Pacific", "facility_code": "SIN", "address": null, "connectivity_options": []}, {"name": "ap-northeast-1", "provider": "AWS", "location": "Tokyo", "city": "Tokyo", "country": "JP", "region": "Asia Pacific", "facility_code": "NRT", "address": null, "connectivity_options": []}], "website": "https://aws.amazon.com"}, "datacenter": null, "connectivity_options": [], "confidence": "medium"}}, {"url": "wss://www.deribit.com/ws/api/v2", "hostname": "www.deribit.com", "dns": {"hostname": "www.deribit.com", "ipv4_addresses": ["*************"], "ipv6_addresses": [], "cname": null, "ttl": 29, "error": null}, "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "*************", "asn": "207856", "asn_description": "DERIBIT, NL", "network": "***********/24", "country": "GB", "city": null, "organization": "mnt-nl-deribit-1", "isp": null, "error": null}], "hosting_infrastructure": {"provider": null, "datacenter": null, "connectivity_options": [], "confidence": "low"}}, {"url": "wss://www.bitmex.com/realtime", "hostname": "www.bitmex.com", "dns": {"hostname": "www.bitmex.com", "ipv4_addresses": ["*************", "*************"], "ipv6_addresses": [], "cname": "www.bitmex.com.cdn.cloudflare.net.", "ttl": 59, "error": null}, "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}], "whois_results": [{"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/13", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Cloudflare", "type": "cdn", "asn_ranges": ["AS13335"], "ip_ranges": ["**********/12", "**********/13"], "datacenters": [], "website": "https://www.cloudflare.com"}, "datacenter": null, "connectivity_options": [], "confidence": "medium"}}, {"url": "wss://stream.crypto.com/v2/market", "hostname": "stream.crypto.com", "dns": {"hostname": "stream.crypto.com", "ipv4_addresses": ["*************", "*************"], "ipv6_addresses": ["2606:4700::6813:de11", "2606:4700::6813:df11"], "cname": null, "ttl": 299, "error": null}, "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": true, "error": null}, {"target": "2606:4700::6813:de11", "target_ip": "2606:4700::6813:de11", "hops": [], "total_hops": 0, "success": false, "error": "connect: Network is unreachable"}], "whois_results": [{"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "**********/12", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}, {"ip_address": "2606:4700::6813:de11", "asn": "13335", "asn_description": "CLOUDFLARENET, US", "network": "2606:4700::/32", "country": null, "city": null, "organization": "Cloudflare, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": {"name": "Cloudflare", "type": "cdn", "asn_ranges": ["AS13335"], "ip_ranges": ["**********/12", "**********/13"], "datacenters": [], "website": "https://www.cloudflare.com"}, "datacenter": null, "connectivity_options": [], "confidence": "medium"}}, {"url": "wss://ftx.com/ws/", "hostname": "ftx.com", "dns": {"hostname": "ftx.com", "ipv4_addresses": ["*************", "*************", "************", "*************"], "ipv6_addresses": [], "cname": null, "ttl": 59, "error": null}, "traceroute_results": [{"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": false, "error": "Traceroute timeout after 30 seconds"}, {"target": "*************", "target_ip": "*************", "hops": [], "total_hops": 0, "success": false, "error": "Traceroute timeout after 30 seconds"}, {"target": "************", "target_ip": "************", "hops": [], "total_hops": 0, "success": false, "error": "Traceroute timeout after 30 seconds"}], "whois_results": [{"ip_address": "*************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/15", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "*************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/15", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}, {"ip_address": "************", "asn": "16509", "asn_description": "AMAZON-02, US", "network": "**********/15", "country": null, "city": null, "organization": "Amazon.com, Inc.", "isp": null, "error": null}], "hosting_infrastructure": {"provider": null, "datacenter": null, "connectivity_options": [], "confidence": "low"}}], "recommendations": [], "summary": {"total_exchanges": 20, "successful_analyses": 20, "failed_analyses": 0, "avg_latency": 0, "best_latency": 0, "worst_latency": 0, "provider_distribution": {"Google Cloud Platform": 1, "Amazon Web Services": 5, "Cloudflare": 8, "Unknown": 6}, "unique_providers": 4}}