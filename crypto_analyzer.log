2025-05-28 10:13:37,759 - datacenter_mapper - WARNING - Custom mapping file not found: datacenter_mapping.json
2025-05-28 10:13:37,759 - crypto_connectivity_analyzer - INFO - Starting analysis of 1 exchange endpoints
2025-05-28 10:14:07,700 - crypto_connectivity_analyzer - INFO - Completed analysis of 1 endpoints
2025-05-28 10:15:09,907 - datacenter_mapper - WARNING - Custom mapping file not found: datacenter_mapping.json
2025-05-28 10:15:09,907 - crypto_connectivity_analyzer - INFO - Starting analysis of 1 exchange endpoints
2025-05-28 10:16:10,988 - datacenter_mapper - WARNING - Custom mapping file not found: datacenter_mapping.json
2025-05-28 10:16:10,988 - crypto_connectivity_analyzer - INFO - Starting analysis of 1 exchange endpoints
2025-05-28 10:16:44,031 - crypto_connectivity_analyzer - INFO - Completed analysis of 1 endpoints
2025-05-28 10:16:51,621 - datacenter_mapper - WARNING - Custom mapping file not found: datacenter_mapping.json
2025-05-28 10:16:51,621 - __main__ - INFO - Starting cryptocurrency exchange connectivity analysis
2025-05-28 10:16:51,621 - __main__ - INFO - Starting analysis of 2 exchange endpoints
2025-05-28 10:17:24,458 - __main__ - INFO - Completed analysis of 2 endpoints
2025-05-28 10:17:24,458 - __main__ - INFO - Generating connectivity recommendations
2025-05-28 10:17:24,458 - __main__ - INFO - Generated 0 recommendations
2025-05-28 10:17:24,458 - __main__ - INFO - Generating reports
2025-05-28 10:17:24,459 - report_generator - INFO - JSON report generated: results/crypto_connectivity_analysis_20250528_101651.json
2025-05-28 10:17:24,459 - __main__ - ERROR - Analysis failed: 'NoneType' object has no attribute 'get'
Traceback (most recent call last):
  File "/home/<USER>/Documents/Projects/CRYPTO_API/networking/augment/crypto_connectivity_analyzer.py", line 196, in run_full_analysis
    report_files = self.generate_reports(analysis_results, recommendations)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Projects/CRYPTO_API/networking/augment/crypto_connectivity_analyzer.py", line 172, in generate_reports
    report_files = self.report_generator.generate_all_reports(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Projects/CRYPTO_API/networking/augment/report_generator.py", line 54, in generate_all_reports
    csv_file = self.generate_csv_report(analysis_results, recommendations)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Projects/CRYPTO_API/networking/augment/report_generator.py", line 150, in generate_csv_report
    datacenter = hosting_info.get('datacenter', {}).get('name', '')
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get'
2025-05-28 10:18:12,389 - datacenter_mapper - WARNING - Custom mapping file not found: datacenter_mapping.json
2025-05-28 10:18:12,390 - __main__ - INFO - Starting cryptocurrency exchange connectivity analysis
2025-05-28 10:18:12,390 - __main__ - INFO - Starting analysis of 2 exchange endpoints
2025-05-28 10:18:44,937 - __main__ - INFO - Completed analysis of 2 endpoints
2025-05-28 10:18:44,937 - __main__ - INFO - Generating connectivity recommendations
2025-05-28 10:18:44,937 - __main__ - INFO - Generated 0 recommendations
2025-05-28 10:18:44,937 - __main__ - INFO - Generating reports
2025-05-28 10:18:44,937 - report_generator - INFO - JSON report generated: results/crypto_connectivity_analysis_20250528_101812.json
2025-05-28 10:18:44,937 - report_generator - INFO - CSV report generated: results/crypto_connectivity_summary_20250528_101812.csv
2025-05-28 10:18:44,941 - report_generator - INFO - HTML report generated: results/crypto_connectivity_report_20250528_101812.html
2025-05-28 10:18:44,941 - report_generator - INFO - Summary report generated: results/crypto_connectivity_summary_20250528_101812.txt
2025-05-28 10:18:44,941 - __main__ - INFO - Generated 4 report files
2025-05-28 10:18:44,941 - __main__ - INFO -   JSON: results/crypto_connectivity_analysis_20250528_101812.json
2025-05-28 10:18:44,941 - __main__ - INFO -   CSV: results/crypto_connectivity_summary_20250528_101812.csv
2025-05-28 10:18:44,941 - __main__ - INFO -   HTML: results/crypto_connectivity_report_20250528_101812.html
2025-05-28 10:18:44,941 - __main__ - INFO -   SUMMARY: results/crypto_connectivity_summary_20250528_101812.txt
2025-05-28 18:10:26,387 - datacenter_mapper - WARNING - Custom mapping file not found: datacenter_mapping.json
2025-05-28 18:10:26,387 - __main__ - INFO - Starting cryptocurrency exchange connectivity analysis
2025-05-28 18:10:26,387 - __main__ - INFO - Starting analysis of 2 exchange endpoints
2025-05-28 18:11:21,340 - __main__ - INFO - Completed analysis of 2 endpoints
2025-05-28 18:11:21,340 - __main__ - INFO - Generating connectivity recommendations
2025-05-28 18:11:21,340 - __main__ - INFO - Generated 0 recommendations
2025-05-28 18:11:21,340 - __main__ - INFO - Generating reports
2025-05-28 18:11:21,341 - report_generator - INFO - JSON report generated: results/crypto_connectivity_analysis_20250528_181026.json
2025-05-28 18:11:21,341 - report_generator - INFO - CSV report generated: results/crypto_connectivity_summary_20250528_181026.csv
2025-05-28 18:11:21,353 - report_generator - INFO - HTML report generated: results/crypto_connectivity_report_20250528_181026.html
2025-05-28 18:11:21,353 - report_generator - INFO - Summary report generated: results/crypto_connectivity_summary_20250528_181026.txt
2025-05-28 18:11:21,353 - __main__ - INFO - Generated 4 report files
2025-05-28 18:11:21,353 - __main__ - INFO -   JSON: results/crypto_connectivity_analysis_20250528_181026.json
2025-05-28 18:11:21,353 - __main__ - INFO -   CSV: results/crypto_connectivity_summary_20250528_181026.csv
2025-05-28 18:11:21,353 - __main__ - INFO -   HTML: results/crypto_connectivity_report_20250528_181026.html
2025-05-28 18:11:21,353 - __main__ - INFO -   SUMMARY: results/crypto_connectivity_summary_20250528_181026.txt
2025-05-28 18:12:44,957 - datacenter_mapper - WARNING - Custom mapping file not found: datacenter_mapping.json
2025-05-28 18:12:44,957 - __main__ - INFO - Starting cryptocurrency exchange connectivity analysis
2025-05-28 18:12:44,958 - __main__ - INFO - Starting analysis of 2 exchange endpoints
2025-05-28 18:13:40,335 - __main__ - INFO - Completed analysis of 2 endpoints
2025-05-28 18:13:40,335 - __main__ - INFO - Generating connectivity recommendations
2025-05-28 18:13:40,335 - __main__ - ERROR - Analysis failed: 'NoneType' object has no attribute 'get'
Traceback (most recent call last):
  File "/home/<USER>/Documents/Projects/CRYPTO_API/networking/augment/crypto_connectivity_analyzer.py", line 193, in run_full_analysis
    recommendations = self.generate_recommendations(analysis_results, requirements)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Projects/CRYPTO_API/networking/augment/crypto_connectivity_analyzer.py", line 160, in generate_recommendations
    recommendations = self.connectivity_recommender.generate_recommendations(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Projects/CRYPTO_API/networking/augment/connectivity_recommender.py", line 278, in generate_recommendations
    datacenter=hosting_info.get('datacenter', {}).get('name', 'Unknown'),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get'
2025-05-28 18:14:26,224 - datacenter_mapper - WARNING - Custom mapping file not found: datacenter_mapping.json
2025-05-28 18:14:26,224 - __main__ - INFO - Starting cryptocurrency exchange connectivity analysis
2025-05-28 18:14:26,224 - __main__ - INFO - Starting analysis of 2 exchange endpoints
2025-05-28 18:15:19,843 - __main__ - INFO - Completed analysis of 2 endpoints
2025-05-28 18:15:19,843 - __main__ - INFO - Generating connectivity recommendations
2025-05-28 18:15:19,843 - __main__ - INFO - Generated 2 recommendations
2025-05-28 18:15:19,844 - __main__ - INFO - Generating reports
2025-05-28 18:15:19,844 - report_generator - INFO - JSON report generated: results/crypto_connectivity_analysis_20250528_181426.json
2025-05-28 18:15:19,844 - report_generator - INFO - CSV report generated: results/crypto_connectivity_summary_20250528_181426.csv
2025-05-28 18:15:19,858 - report_generator - INFO - HTML report generated: results/crypto_connectivity_report_20250528_181426.html
2025-05-28 18:15:19,858 - report_generator - INFO - Summary report generated: results/crypto_connectivity_summary_20250528_181426.txt
2025-05-28 18:15:19,858 - __main__ - INFO - Generated 4 report files
2025-05-28 18:15:19,858 - __main__ - INFO -   JSON: results/crypto_connectivity_analysis_20250528_181426.json
2025-05-28 18:15:19,858 - __main__ - INFO -   CSV: results/crypto_connectivity_summary_20250528_181426.csv
2025-05-28 18:15:19,858 - __main__ - INFO -   HTML: results/crypto_connectivity_report_20250528_181426.html
2025-05-28 18:15:19,858 - __main__ - INFO -   SUMMARY: results/crypto_connectivity_summary_20250528_181426.txt
2025-05-28 18:15:59,162 - datacenter_mapper - WARNING - Custom mapping file not found: datacenter_mapping.json
2025-05-28 18:15:59,163 - __main__ - INFO - Starting cryptocurrency exchange connectivity analysis
2025-05-28 18:15:59,163 - __main__ - INFO - Starting analysis of 3 exchange endpoints
2025-05-28 18:16:54,742 - __main__ - INFO - Completed analysis of 3 endpoints
2025-05-28 18:16:54,742 - __main__ - INFO - Generating connectivity recommendations
2025-05-28 18:16:54,742 - __main__ - INFO - Generated 3 recommendations
2025-05-28 18:16:54,742 - __main__ - INFO - Generating reports
2025-05-28 18:16:54,743 - report_generator - INFO - JSON report generated: results/crypto_connectivity_analysis_20250528_181559.json
2025-05-28 18:16:54,743 - report_generator - INFO - CSV report generated: results/crypto_connectivity_summary_20250528_181559.csv
2025-05-28 18:16:54,758 - report_generator - INFO - HTML report generated: results/crypto_connectivity_report_20250528_181559.html
2025-05-28 18:16:54,758 - report_generator - INFO - Summary report generated: results/crypto_connectivity_summary_20250528_181559.txt
2025-05-28 18:16:54,758 - __main__ - INFO - Generated 4 report files
2025-05-28 18:16:54,758 - __main__ - INFO -   JSON: results/crypto_connectivity_analysis_20250528_181559.json
2025-05-28 18:16:54,758 - __main__ - INFO -   CSV: results/crypto_connectivity_summary_20250528_181559.csv
2025-05-28 18:16:54,758 - __main__ - INFO -   HTML: results/crypto_connectivity_report_20250528_181559.html
2025-05-28 18:16:54,758 - __main__ - INFO -   SUMMARY: results/crypto_connectivity_summary_20250528_181559.txt
