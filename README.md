# Cryptocurrency Exchange Market Data Connectivity Analyzer

A comprehensive Python program that analyzes connectivity to cryptocurrency exchange market data websocket endpoints, providing detailed network analysis, datacenter identification, and connectivity recommendations for optimal low-latency access.

## Features

### 🔍 **Comprehensive Exchange Coverage**
- **20+ Major Exchanges**: Binance, Coinbase, Kraken, OKX, Huobi, Bybit, KuCoin, Gate.io, Bitfinex, and more
- **Market Data Types**: Order book (L1/L2/L3), trades, tickers, funding rates, liquidations
- **Market-by-Order (MBO)** data endpoint identification where available
- **Regional Variants**: US-specific endpoints (Binance US, Coinbase Advanced Trade)

### 🌐 **Network Analysis**
- **DNS Resolution**: IPv4/IPv6 address mapping with TTL and CNAME detection
- **Route Analysis**: Traceroute/MTR for latency and hop analysis
- **WHOIS Lookups**: Network ownership, ASN, and geographic information
- **Concurrent Processing**: Async analysis with configurable rate limiting

### 🏢 **Datacenter Identification**
- **Hosting Provider Detection**: AWS, GCP, Azure, Equinix, DigitalOcean, and more
- **Specific Datacenter Mapping**: Facility codes, locations, and connectivity options
- **ASN-based Classification**: Automatic provider identification via network ranges
- **Geographic Mapping**: Country, city, and region identification

### 💡 **Connectivity Recommendations**
- **Multiple Connection Types**: VPS, colocation, cross-connects, direct connections
- **Cost Estimates**: Monthly pricing ranges for different connectivity options
- **Latency Predictions**: Sub-millisecond to multi-millisecond estimates
- **Setup Time Estimates**: From minutes (cloud VPS) to weeks (colocation)
- **Confidence Scoring**: Algorithm-based recommendation reliability

### 📊 **Comprehensive Reporting**
- **Multiple Formats**: JSON, CSV, HTML, and text summary reports
- **Interactive HTML**: Rich visualizations and detailed analysis
- **Executive Summary**: High-level insights and key metrics
- **Detailed Analysis**: Per-exchange breakdown with technical details

## Installation

### Prerequisites
- Python 3.9 or higher
- Linux/macOS (Windows supported with limitations)
- Network tools: `traceroute`, `dig`, `nslookup` (usually pre-installed)

### Quick Install
```bash
# Clone the repository
git clone <repository-url>
cd crypto-connectivity-analyzer

# Install dependencies
pip install -r requirements.txt

# Run the analyzer
python crypto_connectivity_analyzer.py
```

### System Dependencies
```bash
# Ubuntu/Debian
sudo apt-get install traceroute dnsutils mtr-tiny

# CentOS/RHEL
sudo yum install traceroute bind-utils mtr

# macOS (with Homebrew)
brew install mtr
```

## Usage

### Basic Usage
```bash
# Analyze all exchanges
python crypto_connectivity_analyzer.py

# Analyze specific exchanges
python crypto_connectivity_analyzer.py --exchanges binance coinbase kraken

# Set custom latency and cost requirements
python crypto_connectivity_analyzer.py --max-latency 5 --max-cost 500

# Use custom configuration
python crypto_connectivity_analyzer.py --config my_config.yaml
```

### Advanced Usage
```bash
# Verbose output with debug logging
python crypto_connectivity_analyzer.py --verbose

# Custom output directory
python crypto_connectivity_analyzer.py --output-dir /path/to/results

# Priority exchanges only
python crypto_connectivity_analyzer.py --exchanges binance coinbase okx huobi
```

## Configuration

The analyzer uses a YAML configuration file (`config.yaml`) for customization:

```yaml
# Network Analysis Settings
network:
  timeout: 30
  max_retries: 3
  concurrent_limit: 10
  traceroute_max_hops: 30

# Output Settings
output:
  formats: ["json", "csv", "html"]
  output_dir: "results"

# Exchange Priority
exchanges:
  priority: ["binance", "coinbase", "kraken", "okx"]
  include_all: true

# Connectivity Analysis
connectivity:
  vps_providers: ["aws", "gcp", "azure", "digitalocean"]
  colo_providers: ["equinix", "digitalrealty"]
  include_costs: true
```

## Output Reports

### 1. JSON Report (`crypto_connectivity_analysis_YYYYMMDD_HHMMSS.json`)
Complete analysis data including:
- DNS resolution results
- Traceroute hop-by-hop analysis
- WHOIS network ownership data
- Hosting infrastructure identification
- Connectivity recommendations with scoring

### 2. CSV Summary (`crypto_connectivity_summary_YYYYMMDD_HHMMSS.csv`)
Tabular data suitable for spreadsheet analysis:
- Exchange names and endpoints
- IP addresses and latency metrics
- Hosting providers and datacenters
- Geographic locations

### 3. HTML Report (`crypto_connectivity_report_YYYYMMDD_HHMMSS.html`)
Interactive web report featuring:
- Executive summary dashboard
- Top connectivity recommendations
- Detailed per-exchange analysis
- Sortable tables and metrics

### 4. Text Summary (`crypto_connectivity_summary_YYYYMMDD_HHMMSS.txt`)
Executive summary with:
- Key performance metrics
- Top 5 connectivity recommendations
- Provider distribution analysis
- Quick reference statistics

## Architecture

### Core Components

1. **ExchangeEndpoints** (`exchange_endpoints.py`)
   - Comprehensive database of exchange websocket endpoints
   - Market data type classification
   - Regional endpoint variants

2. **NetworkAnalyzer** (`network_analyzer.py`)
   - DNS resolution with IPv4/IPv6 support
   - Traceroute/MTR execution and parsing
   - WHOIS data collection and processing
   - Concurrent analysis with rate limiting

3. **DatacenterMapper** (`datacenter_mapper.py`)
   - Hosting provider identification via ASN/IP ranges
   - Specific datacenter facility mapping
   - Connectivity option enumeration

4. **ConnectivityRecommender** (`connectivity_recommender.py`)
   - Multi-factor scoring algorithm
   - Cost-benefit analysis
   - Latency optimization recommendations
   - Implementation guidance

5. **ReportGenerator** (`report_generator.py`)
   - Multi-format output generation
   - Data visualization and formatting
   - Executive summary creation

## Supported Exchanges

| Exchange | Endpoint Type | Order Book | Region | Notes |
|----------|---------------|------------|---------|-------|
| Binance | WebSocket | L2 | Global | Multiple regional endpoints |
| Coinbase | WebSocket | L3 | Global | Advanced Trade API |
| Kraken | WebSocket | L2 | Global | Spot + Futures |
| OKX | WebSocket | L2 | Global | Comprehensive derivatives |
| Huobi | WebSocket | L2 | Global | Spot + Futures |
| Bybit | WebSocket | L2 | Global | Spot + Derivatives |
| KuCoin | WebSocket | L2 | Global | Token-based auth |
| Gate.io | WebSocket | L2 | Global | V4 API |
| Bitfinex | WebSocket | L2 | Global | Funding data |
| Bitstamp | WebSocket | L2 | Europe | European focus |
| Gemini | WebSocket | L2 | US | US-regulated |
| Deribit | WebSocket | L2 | Global | Options specialist |
| BitMEX | WebSocket | L2 | Global | Derivatives focus |
| Crypto.com | WebSocket | L2 | Global | Growing exchange |

## Connectivity Options Analysis

### Cloud Providers
- **AWS**: EC2 instances, Direct Connect, multiple regions
- **Google Cloud**: Compute Engine, Dedicated Interconnect
- **Microsoft Azure**: Virtual Machines, ExpressRoute
- **DigitalOcean**: Droplets, simple pricing

### Colocation Providers
- **Equinix**: Global presence, cross-connects, premium facilities
- **Digital Realty**: Major markets, carrier-neutral
- **Cyxtera**: Enterprise-focused, security emphasis

### Cost Estimates (Monthly USD)
- **Basic VPS**: $10-50 (2-10ms latency)
- **Premium VPS**: $50-200 (1-5ms latency)
- **Dedicated Servers**: $100-500 (1-3ms latency)
- **Colocation**: $1000-5000 (<1ms latency)
- **Cross-Connects**: $100-500 (<0.5ms latency)

## Performance Considerations

### Latency Targets
- **Retail Trading**: <50ms acceptable
- **Professional Trading**: <10ms recommended
- **High-Frequency Trading**: <1ms required
- **Market Making**: <0.5ms optimal

### Bandwidth Requirements
- **Order Book Updates**: 1-10 Mbps per exchange
- **Trade Data**: 100 Kbps - 1 Mbps per exchange
- **Multiple Exchanges**: 10-100 Mbps total

## Troubleshooting

### Common Issues

1. **Permission Errors**
   ```bash
   # Run with sudo if traceroute requires privileges
   sudo python crypto_connectivity_analyzer.py
   ```

2. **Missing Network Tools**
   ```bash
   # Install required tools
   sudo apt-get install traceroute dnsutils mtr-tiny
   ```

3. **Timeout Issues**
   ```bash
   # Increase timeout in config.yaml
   network:
     timeout: 60
   ```

4. **Rate Limiting**
   ```bash
   # Reduce concurrent connections
   network:
     concurrent_limit: 5
   ```

### Debug Mode
```bash
# Enable verbose logging
python crypto_connectivity_analyzer.py --verbose
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add new exchange endpoints to `exchange_endpoints.py`
4. Update datacenter mappings in `datacenter_mapper.py`
5. Add tests for new functionality
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This tool is for informational purposes only. Network conditions, costs, and availability can change rapidly. Always verify current pricing and performance with providers before making infrastructure decisions.

## Support

For issues, questions, or contributions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the configuration options
- Enable debug logging for detailed analysis
