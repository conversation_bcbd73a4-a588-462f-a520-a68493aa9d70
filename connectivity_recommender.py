"""
Connectivity Recommendation Engine
Analyzes network data and provides recommendations for optimal connectivity to exchanges.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime

@dataclass
class ConnectivityRecommendation:
    """Recommendation for connecting to an exchange."""
    exchange_name: str
    provider: str
    datacenter: str
    connection_type: str
    estimated_latency: str
    monthly_cost_range: str
    setup_time: str
    confidence_score: float
    pros: List[str]
    cons: List[str]
    next_steps: List[str]

@dataclass
class LatencyAnalysis:
    """Analysis of network latency to an endpoint."""
    min_latency_ms: float
    avg_latency_ms: float
    max_latency_ms: float
    jitter_ms: float
    packet_loss_percent: float
    hop_count: int
    bottleneck_hop: Optional[int] = None

class ConnectivityRecommender:
    """Generates connectivity recommendations based on network analysis."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.cost_database = self._load_cost_database()
    
    def _load_cost_database(self) -> Dict[str, Dict[str, Any]]:
        """Load cost estimates for different connectivity options."""
        return {
            "aws": {
                "ec2_micro": {"cost": 10, "description": "t3.micro instance"},
                "ec2_small": {"cost": 20, "description": "t3.small instance"},
                "ec2_medium": {"cost": 40, "description": "t3.medium instance"},
                "ec2_large": {"cost": 80, "description": "t3.large instance"},
                "direct_connect": {"cost": 500, "description": "1Gbps Direct Connect"},
            },
            "gcp": {
                "compute_micro": {"cost": 8, "description": "e2-micro instance"},
                "compute_small": {"cost": 15, "description": "e2-small instance"},
                "compute_medium": {"cost": 35, "description": "e2-medium instance"},
                "dedicated_interconnect": {"cost": 1000, "description": "10Gbps Dedicated Interconnect"},
            },
            "azure": {
                "vm_b1s": {"cost": 12, "description": "B1s VM"},
                "vm_b2s": {"cost": 25, "description": "B2s VM"},
                "expressroute": {"cost": 800, "description": "1Gbps ExpressRoute"},
            },
            "digitalocean": {
                "droplet_basic": {"cost": 6, "description": "Basic Droplet 1GB"},
                "droplet_standard": {"cost": 12, "description": "Standard Droplet 2GB"},
                "droplet_premium": {"cost": 24, "description": "Premium Droplet 4GB"},
            },
            "equinix": {
                "quarter_rack": {"cost": 1500, "description": "1/4 rack colocation"},
                "half_rack": {"cost": 2500, "description": "1/2 rack colocation"},
                "full_rack": {"cost": 4000, "description": "Full rack colocation"},
                "cross_connect": {"cost": 200, "description": "Cross connect fee"},
            },
            "vultr": {
                "regular_1gb": {"cost": 6, "description": "Regular 1GB instance"},
                "high_frequency": {"cost": 12, "description": "High Frequency 1GB"},
                "bare_metal": {"cost": 120, "description": "Bare Metal server"},
            }
        }
    
    def analyze_latency(self, ping_results: List[Dict[str, Any]] = None,
                       traceroute_results: List[Dict[str, Any]] = None) -> LatencyAnalysis:
        """Analyze latency characteristics from ping and traceroute data."""
        if ping_results is None:
            ping_results = []
        if traceroute_results is None:
            traceroute_results = []

        # Initialize default values
        min_latency = max_latency = avg_latency = jitter = packet_loss = 0.0
        hop_count = 0
        bottleneck_hop = None

        # Prefer ping results for latency analysis (more accurate)
        if ping_results:
            successful_pings = [p for p in ping_results if p.get('success')]
            if successful_pings:
                ping = successful_pings[0]  # Use first successful ping
                min_latency = ping.get('min_rtt_ms', 0)
                max_latency = ping.get('max_rtt_ms', 0)
                avg_latency = ping.get('avg_rtt_ms', 0)
                packet_loss = ping.get('packet_loss_percent', 0)

                # Calculate jitter from ping variance (if available)
                # For now, estimate jitter as a percentage of average latency
                jitter = avg_latency * 0.1  # Rough estimate

        # Fallback to traceroute for latency if ping failed
        if avg_latency == 0 and traceroute_results:
            all_rtts = []
            max_hop_latency = 0

            for tr_result in traceroute_results:
                if tr_result.get('success') and tr_result.get('hops'):
                    hops = tr_result['hops']
                    hop_count = max(hop_count, len(hops))

                    for hop in hops:
                        if hop.get('rtt_ms'):
                            all_rtts.extend(hop['rtt_ms'])

                            # Find bottleneck hop (highest latency increase)
                            avg_rtt = hop.get('avg_rtt_ms', 0)
                            if avg_rtt > max_hop_latency:
                                max_hop_latency = avg_rtt
                                bottleneck_hop = hop.get('hop_number')

            if all_rtts:
                min_latency = min(all_rtts)
                max_latency = max(all_rtts)
                avg_latency = sum(all_rtts) / len(all_rtts)

                # Calculate jitter (standard deviation)
                variance = sum((x - avg_latency) ** 2 for x in all_rtts) / len(all_rtts)
                jitter = variance ** 0.5

        # Get hop count from traceroute even if ping was successful
        if traceroute_results:
            for tr_result in traceroute_results:
                if tr_result.get('success') and tr_result.get('hops'):
                    hop_count = max(hop_count, len(tr_result['hops']))

        return LatencyAnalysis(
            min_latency_ms=min_latency,
            avg_latency_ms=avg_latency,
            max_latency_ms=max_latency,
            jitter_ms=jitter,
            packet_loss_percent=packet_loss,
            hop_count=hop_count,
            bottleneck_hop=bottleneck_hop
        )
    
    def score_connectivity_option(self, option: Dict[str, Any], 
                                latency_analysis: LatencyAnalysis,
                                requirements: Dict[str, Any] = None) -> float:
        """Score a connectivity option based on requirements and performance."""
        if requirements is None:
            requirements = {"max_latency_ms": 10, "max_cost_usd": 1000}
        
        score = 0.0
        
        # Latency score (40% weight)
        max_acceptable_latency = requirements.get("max_latency_ms", 10)
        if latency_analysis.avg_latency_ms <= max_acceptable_latency:
            latency_score = 1.0 - (latency_analysis.avg_latency_ms / max_acceptable_latency)
            score += latency_score * 0.4
        
        # Cost score (30% weight)
        max_acceptable_cost = requirements.get("max_cost_usd", 1000)
        cost_estimate = self._extract_cost_from_option(option)
        if cost_estimate <= max_acceptable_cost:
            cost_score = 1.0 - (cost_estimate / max_acceptable_cost)
            score += cost_score * 0.3
        
        # Reliability score (20% weight)
        reliability_score = self._assess_reliability(option)
        score += reliability_score * 0.2
        
        # Setup complexity score (10% weight)
        setup_score = self._assess_setup_complexity(option)
        score += setup_score * 0.1
        
        return min(score, 1.0)
    
    def _extract_cost_from_option(self, option: Dict[str, Any]) -> float:
        """Extract cost estimate from connectivity option."""
        cost_str = option.get("cost_estimate", "$0/month")
        
        # Parse cost range like "$50-500/month"
        import re
        match = re.search(r'\$(\d+)(?:-(\d+))?', cost_str)
        if match:
            min_cost = int(match.group(1))
            max_cost = int(match.group(2)) if match.group(2) else min_cost
            return (min_cost + max_cost) / 2
        
        return 0.0
    
    def _assess_reliability(self, option: Dict[str, Any]) -> float:
        """Assess reliability of connectivity option."""
        option_type = option.get("type", "").lower()
        
        reliability_scores = {
            "colocation": 0.95,
            "cross connect": 0.98,
            "direct connect": 0.95,
            "dedicated interconnect": 0.95,
            "expressroute": 0.95,
            "ec2 instance": 0.85,
            "vm instance": 0.85,
            "droplet": 0.80,
            "bare metal": 0.90,
        }
        
        for key, score in reliability_scores.items():
            if key in option_type:
                return score
        
        return 0.7  # Default score
    
    def _assess_setup_complexity(self, option: Dict[str, Any]) -> float:
        """Assess setup complexity (higher score = easier setup)."""
        setup_time = option.get("setup_time", "").lower()
        
        if "minutes" in setup_time:
            return 1.0
        elif "hours" in setup_time:
            return 0.8
        elif "days" in setup_time:
            return 0.6
        elif "weeks" in setup_time:
            return 0.4
        elif "months" in setup_time:
            return 0.2
        
        return 0.5  # Default score
    
    def generate_recommendations(self, analysis_results: List[Dict[str, Any]], 
                               requirements: Dict[str, Any] = None) -> List[ConnectivityRecommendation]:
        """Generate connectivity recommendations for all analyzed exchanges."""
        recommendations = []
        
        for result in analysis_results:
            if result.get('error'):
                continue

            exchange_name = self._extract_exchange_name(result.get('url', ''))
            latency_analysis = self.analyze_latency(
                ping_results=result.get('ping_results', []),
                traceroute_results=result.get('traceroute_results', [])
            )
            
            # Get hosting infrastructure analysis
            hosting_info = result.get('hosting_infrastructure', {})
            connectivity_options = hosting_info.get('connectivity_options', [])
            
            if not connectivity_options:
                continue
            
            # Score and rank options
            scored_options = []
            for option in connectivity_options:
                score = self.score_connectivity_option(option, latency_analysis, requirements)
                scored_options.append((option, score))
            
            # Sort by score (highest first)
            scored_options.sort(key=lambda x: x[1], reverse=True)
            
            # Generate recommendation for best option
            if scored_options:
                best_option, best_score = scored_options[0]
                
                # Safely extract provider and datacenter info
                provider_info = hosting_info.get('provider', {}) or {}
                datacenter_info = hosting_info.get('datacenter', {}) or {}

                recommendation = ConnectivityRecommendation(
                    exchange_name=exchange_name,
                    provider=provider_info.get('name', 'Unknown'),
                    datacenter=datacenter_info.get('name', 'Unknown'),
                    connection_type=best_option.get('type', 'Unknown'),
                    estimated_latency=best_option.get('latency_estimate', 'Unknown'),
                    monthly_cost_range=best_option.get('cost_estimate', 'Unknown'),
                    setup_time=best_option.get('setup_time', 'Unknown'),
                    confidence_score=best_score,
                    pros=self._generate_pros(best_option, latency_analysis),
                    cons=self._generate_cons(best_option, latency_analysis),
                    next_steps=self._generate_next_steps(best_option, hosting_info)
                )
                
                recommendations.append(recommendation)
        
        # Sort recommendations by confidence score
        recommendations.sort(key=lambda x: x.confidence_score, reverse=True)
        
        return recommendations
    
    def _extract_exchange_name(self, url: str) -> str:
        """Extract exchange name from websocket URL."""
        from urllib.parse import urlparse
        hostname = urlparse(url).hostname or url
        
        # Simple mapping based on hostname patterns
        if 'binance' in hostname:
            return 'Binance'
        elif 'coinbase' in hostname:
            return 'Coinbase'
        elif 'kraken' in hostname:
            return 'Kraken'
        elif 'okx' in hostname or 'okex' in hostname:
            return 'OKX'
        elif 'huobi' in hostname:
            return 'Huobi'
        elif 'bybit' in hostname:
            return 'Bybit'
        elif 'kucoin' in hostname:
            return 'KuCoin'
        elif 'gateio' in hostname or 'gate.io' in hostname:
            return 'Gate.io'
        elif 'bitfinex' in hostname:
            return 'Bitfinex'
        elif 'bitstamp' in hostname:
            return 'Bitstamp'
        elif 'gemini' in hostname:
            return 'Gemini'
        elif 'deribit' in hostname:
            return 'Deribit'
        elif 'bitmex' in hostname:
            return 'BitMEX'
        elif 'crypto.com' in hostname:
            return 'Crypto.com'
        elif 'ftx' in hostname:
            return 'FTX'
        else:
            return hostname.split('.')[0].title()
    
    def _generate_pros(self, option: Dict[str, Any], 
                      latency_analysis: LatencyAnalysis) -> List[str]:
        """Generate pros for a connectivity option."""
        pros = []
        
        if latency_analysis.avg_latency_ms < 5:
            pros.append("Very low latency (< 5ms)")
        elif latency_analysis.avg_latency_ms < 10:
            pros.append("Low latency (< 10ms)")
        
        if "minutes" in option.get("setup_time", "").lower():
            pros.append("Quick setup time")
        
        cost = self._extract_cost_from_option(option)
        if cost < 100:
            pros.append("Cost-effective solution")
        
        option_type = option.get("type", "").lower()
        if "colocation" in option_type or "cross connect" in option_type:
            pros.append("Direct physical connection")
            pros.append("Maximum performance and control")
        
        if "cloud" in option_type or "instance" in option_type:
            pros.append("Scalable and flexible")
            pros.append("Managed infrastructure")
        
        return pros
    
    def _generate_cons(self, option: Dict[str, Any], 
                      latency_analysis: LatencyAnalysis) -> List[str]:
        """Generate cons for a connectivity option."""
        cons = []
        
        if latency_analysis.avg_latency_ms > 20:
            cons.append("Higher latency (> 20ms)")
        
        if latency_analysis.jitter_ms > 5:
            cons.append("High jitter/variability")
        
        cost = self._extract_cost_from_option(option)
        if cost > 1000:
            cons.append("High monthly cost")
        
        if "weeks" in option.get("setup_time", "").lower():
            cons.append("Long setup time")
        
        option_type = option.get("type", "").lower()
        if "colocation" in option_type:
            cons.append("Requires physical presence")
            cons.append("High upfront investment")
        
        if "shared" in option_type or "basic" in option_type:
            cons.append("Shared infrastructure")
        
        return cons
    
    def _generate_next_steps(self, option: Dict[str, Any],
                           hosting_info: Dict[str, Any]) -> List[str]:
        """Generate next steps for implementing the connectivity option."""
        steps = []

        provider_info = hosting_info.get('provider', {}) or {}
        provider_name = provider_info.get('name', '')
        option_type = option.get("type", "").lower()
        
        if "aws" in provider_name.lower():
            steps.extend([
                "Create AWS account if not already available",
                "Select appropriate EC2 instance type and region",
                "Configure security groups and networking",
                "Deploy trading application"
            ])
        elif "colocation" in option_type:
            steps.extend([
                "Contact colocation provider for space availability",
                "Plan hardware requirements and procurement",
                "Arrange for installation and setup",
                "Configure network connectivity and cross-connects"
            ])
        elif "cloud" in option_type or "instance" in option_type:
            steps.extend([
                "Create account with cloud provider",
                "Select appropriate instance size and location",
                "Deploy and configure trading infrastructure",
                "Test connectivity and performance"
            ])
        
        steps.append("Conduct thorough latency and performance testing")
        steps.append("Implement monitoring and alerting")
        
        return steps
