"""
Report Generation Module
Generates comprehensive reports in multiple formats (JSON, CSV, HTML).
"""

import json
import csv
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import asdict

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

try:
    from jinja2 import Template
    JINJA2_AVAILABLE = True
except ImportError:
    JINJA2_AVAILABLE = False

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

class ReportGenerator:
    """Generates comprehensive analysis reports in multiple formats."""
    
    def __init__(self, output_dir: str = "results"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def generate_all_reports(self, analysis_results: List[Dict[str, Any]], 
                           recommendations: List[Any],
                           config: Dict[str, Any] = None) -> Dict[str, str]:
        """Generate all report formats."""
        report_files = {}
        
        # JSON Report
        json_file = self.generate_json_report(analysis_results, recommendations)
        report_files['json'] = json_file
        
        # CSV Report
        csv_file = self.generate_csv_report(analysis_results, recommendations)
        report_files['csv'] = csv_file
        
        # HTML Report
        if JINJA2_AVAILABLE:
            html_file = self.generate_html_report(analysis_results, recommendations)
            report_files['html'] = html_file
        
        # Summary Report
        summary_file = self.generate_summary_report(analysis_results, recommendations)
        report_files['summary'] = summary_file
        
        return report_files
    
    def generate_json_report(self, analysis_results: List[Dict[str, Any]], 
                           recommendations: List[Any]) -> str:
        """Generate comprehensive JSON report."""
        filename = self.output_dir / f"crypto_connectivity_analysis_{self.timestamp}.json"
        
        # Convert recommendations to dict format
        recommendations_dict = []
        for rec in recommendations:
            if hasattr(rec, '__dict__'):
                recommendations_dict.append(asdict(rec))
            else:
                recommendations_dict.append(rec)
        
        report_data = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "version": "1.0",
                "total_exchanges_analyzed": len(analysis_results),
                "total_recommendations": len(recommendations)
            },
            "analysis_results": analysis_results,
            "recommendations": recommendations_dict,
            "summary": self._generate_summary_stats(analysis_results, recommendations)
        }
        
        with open(filename, 'w') as f:
            json.dump(report_data, f, indent=2, default=str)
        
        self.logger.info(f"JSON report generated: {filename}")
        return str(filename)
    
    def generate_csv_report(self, analysis_results: List[Dict[str, Any]], 
                          recommendations: List[Any]) -> str:
        """Generate CSV report with key metrics."""
        filename = self.output_dir / f"crypto_connectivity_summary_{self.timestamp}.csv"
        
        # Prepare data for CSV
        csv_data = []
        
        for result in analysis_results:
            if result.get('error'):
                continue
            
            # Extract key information
            url = result.get('url', '')
            hostname = result.get('hostname', '')
            
            # DNS info
            dns_info = result.get('dns', {})
            ipv4_addresses = ', '.join(dns_info.get('ipv4_addresses', []))
            ipv6_addresses = ', '.join(dns_info.get('ipv6_addresses', []))
            
            # Latency info from traceroute
            traceroute_results = result.get('traceroute_results', [])
            min_latency = max_latency = avg_latency = hop_count = 0
            
            if traceroute_results:
                all_rtts = []
                for tr in traceroute_results:
                    if tr.get('success') and tr.get('hops'):
                        for hop in tr['hops']:
                            if hop.get('rtt_ms'):
                                all_rtts.extend(hop['rtt_ms'])
                        hop_count = max(hop_count, len(tr['hops']))
                
                if all_rtts:
                    min_latency = min(all_rtts)
                    max_latency = max(all_rtts)
                    avg_latency = sum(all_rtts) / len(all_rtts)
            
            # WHOIS info
            whois_results = result.get('whois_results', [])
            asn = organization = country = ''
            if whois_results:
                first_whois = whois_results[0]
                asn = first_whois.get('asn', '')
                organization = first_whois.get('organization', '')
                country = first_whois.get('country', '')
            
            # Hosting info
            hosting_info = result.get('hosting_infrastructure', {}) or {}
            provider_info = hosting_info.get('provider', {}) or {}
            datacenter_info = hosting_info.get('datacenter', {}) or {}
            provider = provider_info.get('name', '')
            datacenter = datacenter_info.get('name', '')
            
            csv_data.append({
                'Exchange': self._extract_exchange_name(url),
                'URL': url,
                'Hostname': hostname,
                'IPv4_Addresses': ipv4_addresses,
                'IPv6_Addresses': ipv6_addresses,
                'Min_Latency_ms': round(min_latency, 2),
                'Avg_Latency_ms': round(avg_latency, 2),
                'Max_Latency_ms': round(max_latency, 2),
                'Hop_Count': hop_count,
                'ASN': asn,
                'Organization': organization,
                'Country': country,
                'Hosting_Provider': provider,
                'Datacenter': datacenter
            })
        
        # Write CSV
        if csv_data:
            with open(filename, 'w', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=csv_data[0].keys())
                writer.writeheader()
                writer.writerows(csv_data)
        
        self.logger.info(f"CSV report generated: {filename}")
        return str(filename)
    
    def generate_html_report(self, analysis_results: List[Dict[str, Any]], 
                           recommendations: List[Any]) -> str:
        """Generate interactive HTML report."""
        filename = self.output_dir / f"crypto_connectivity_report_{self.timestamp}.html"
        
        # HTML template
        html_template = """
<!DOCTYPE html>
<html>
<head>
    <title>Cryptocurrency Exchange Connectivity Analysis</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .exchange { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .metric { display: inline-block; margin: 5px 10px; padding: 5px; background-color: #e9e9e9; border-radius: 3px; }
        .recommendation { background-color: #f9f9f9; padding: 15px; margin: 10px 0; border-left: 4px solid #007cba; }
        .high-confidence { border-left-color: #28a745; }
        .medium-confidence { border-left-color: #ffc107; }
        .low-confidence { border-left-color: #dc3545; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Cryptocurrency Exchange Connectivity Analysis</h1>
        <p>Generated on: {{ timestamp }}</p>
        <p>Total Exchanges Analyzed: {{ total_exchanges }}</p>
        <p>Total Recommendations: {{ total_recommendations }}</p>
    </div>
    
    <div class="section">
        <h2>Executive Summary</h2>
        <div class="metric">Average Latency: {{ avg_latency }}ms</div>
        <div class="metric">Best Latency: {{ best_latency }}ms</div>
        <div class="metric">Worst Latency: {{ worst_latency }}ms</div>
        <div class="metric">Total Unique Providers: {{ unique_providers }}</div>
    </div>
    
    <div class="section">
        <h2>Top Recommendations</h2>
        {% for rec in top_recommendations %}
        <div class="recommendation {{ rec.confidence_class }}">
            <h3>{{ rec.exchange_name }} - {{ rec.connection_type }}</h3>
            <p><strong>Provider:</strong> {{ rec.provider }}</p>
            <p><strong>Datacenter:</strong> {{ rec.datacenter }}</p>
            <p><strong>Estimated Latency:</strong> {{ rec.estimated_latency }}</p>
            <p><strong>Monthly Cost:</strong> {{ rec.monthly_cost_range }}</p>
            <p><strong>Setup Time:</strong> {{ rec.setup_time }}</p>
            <p><strong>Confidence Score:</strong> {{ rec.confidence_score }}</p>
        </div>
        {% endfor %}
    </div>
    
    <div class="section">
        <h2>Detailed Analysis</h2>
        <table>
            <tr>
                <th>Exchange</th>
                <th>Hostname</th>
                <th>IP Addresses</th>
                <th>Latency (ms)</th>
                <th>Hops</th>
                <th>Provider</th>
                <th>Country</th>
            </tr>
            {% for result in detailed_results %}
            <tr>
                <td>{{ result.exchange }}</td>
                <td>{{ result.hostname }}</td>
                <td>{{ result.ip_addresses }}</td>
                <td>{{ result.latency }}</td>
                <td>{{ result.hops }}</td>
                <td>{{ result.provider }}</td>
                <td>{{ result.country }}</td>
            </tr>
            {% endfor %}
        </table>
    </div>
</body>
</html>
        """
        
        # Prepare template data
        template_data = self._prepare_html_template_data(analysis_results, recommendations)
        
        if JINJA2_AVAILABLE:
            template = Template(html_template)
            html_content = template.render(**template_data)
            
            with open(filename, 'w') as f:
                f.write(html_content)
        
        self.logger.info(f"HTML report generated: {filename}")
        return str(filename)
    
    def generate_summary_report(self, analysis_results: List[Dict[str, Any]], 
                              recommendations: List[Any]) -> str:
        """Generate executive summary text report."""
        filename = self.output_dir / f"crypto_connectivity_summary_{self.timestamp}.txt"
        
        summary_stats = self._generate_summary_stats(analysis_results, recommendations)
        
        with open(filename, 'w') as f:
            f.write("CRYPTOCURRENCY EXCHANGE CONNECTIVITY ANALYSIS\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("EXECUTIVE SUMMARY\n")
            f.write("-" * 20 + "\n")
            f.write(f"Total Exchanges Analyzed: {summary_stats['total_exchanges']}\n")
            f.write(f"Successful Analyses: {summary_stats['successful_analyses']}\n")
            f.write(f"Failed Analyses: {summary_stats['failed_analyses']}\n")
            f.write(f"Average Latency: {summary_stats['avg_latency']:.2f}ms\n")
            f.write(f"Best Latency: {summary_stats['best_latency']:.2f}ms\n")
            f.write(f"Worst Latency: {summary_stats['worst_latency']:.2f}ms\n\n")
            
            f.write("TOP RECOMMENDATIONS\n")
            f.write("-" * 20 + "\n")
            
            # Sort recommendations by confidence score
            sorted_recs = sorted(recommendations, 
                               key=lambda x: getattr(x, 'confidence_score', 0), 
                               reverse=True)
            
            for i, rec in enumerate(sorted_recs[:5], 1):
                f.write(f"{i}. {rec.exchange_name}\n")
                f.write(f"   Provider: {rec.provider}\n")
                f.write(f"   Connection: {rec.connection_type}\n")
                f.write(f"   Latency: {rec.estimated_latency}\n")
                f.write(f"   Cost: {rec.monthly_cost_range}\n")
                f.write(f"   Confidence: {rec.confidence_score:.2f}\n\n")
            
            f.write("PROVIDER DISTRIBUTION\n")
            f.write("-" * 20 + "\n")
            provider_counts = summary_stats.get('provider_distribution', {})
            for provider, count in provider_counts.items():
                f.write(f"{provider}: {count} exchanges\n")
        
        self.logger.info(f"Summary report generated: {filename}")
        return str(filename)
    
    def _generate_summary_stats(self, analysis_results: List[Dict[str, Any]], 
                              recommendations: List[Any]) -> Dict[str, Any]:
        """Generate summary statistics."""
        total_exchanges = len(analysis_results)
        successful_analyses = len([r for r in analysis_results if not r.get('error')])
        failed_analyses = total_exchanges - successful_analyses
        
        # Calculate latency statistics
        all_latencies = []
        provider_counts = {}
        
        for result in analysis_results:
            if result.get('error'):
                continue
            
            # Extract latencies
            traceroute_results = result.get('traceroute_results', [])
            for tr in traceroute_results:
                if tr.get('success') and tr.get('hops'):
                    for hop in tr['hops']:
                        if hop.get('rtt_ms'):
                            all_latencies.extend(hop['rtt_ms'])
            
            # Count providers
            hosting_info = result.get('hosting_infrastructure', {}) or {}
            provider_info = hosting_info.get('provider', {}) or {}
            provider = provider_info.get('name', 'Unknown')
            provider_counts[provider] = provider_counts.get(provider, 0) + 1
        
        avg_latency = sum(all_latencies) / len(all_latencies) if all_latencies else 0
        best_latency = min(all_latencies) if all_latencies else 0
        worst_latency = max(all_latencies) if all_latencies else 0
        
        return {
            'total_exchanges': total_exchanges,
            'successful_analyses': successful_analyses,
            'failed_analyses': failed_analyses,
            'avg_latency': avg_latency,
            'best_latency': best_latency,
            'worst_latency': worst_latency,
            'provider_distribution': provider_counts,
            'unique_providers': len(provider_counts)
        }
    
    def _prepare_html_template_data(self, analysis_results: List[Dict[str, Any]], 
                                  recommendations: List[Any]) -> Dict[str, Any]:
        """Prepare data for HTML template."""
        summary_stats = self._generate_summary_stats(analysis_results, recommendations)
        
        # Prepare top recommendations
        top_recommendations = []
        sorted_recs = sorted(recommendations, 
                           key=lambda x: getattr(x, 'confidence_score', 0), 
                           reverse=True)
        
        for rec in sorted_recs[:5]:
            confidence_class = "high-confidence" if rec.confidence_score > 0.8 else \
                             "medium-confidence" if rec.confidence_score > 0.5 else \
                             "low-confidence"
            
            top_recommendations.append({
                'exchange_name': rec.exchange_name,
                'provider': rec.provider,
                'datacenter': rec.datacenter,
                'connection_type': rec.connection_type,
                'estimated_latency': rec.estimated_latency,
                'monthly_cost_range': rec.monthly_cost_range,
                'setup_time': rec.setup_time,
                'confidence_score': f"{rec.confidence_score:.2f}",
                'confidence_class': confidence_class
            })
        
        # Prepare detailed results
        detailed_results = []
        for result in analysis_results:
            if result.get('error'):
                continue
            
            dns_info = result.get('dns', {})
            all_ips = dns_info.get('ipv4_addresses', []) + dns_info.get('ipv6_addresses', [])
            
            # Calculate average latency
            traceroute_results = result.get('traceroute_results', [])
            all_rtts = []
            hop_count = 0
            
            for tr in traceroute_results:
                if tr.get('success') and tr.get('hops'):
                    for hop in tr['hops']:
                        if hop.get('rtt_ms'):
                            all_rtts.extend(hop['rtt_ms'])
                    hop_count = max(hop_count, len(tr['hops']))
            
            avg_latency = sum(all_rtts) / len(all_rtts) if all_rtts else 0
            
            # Get provider info
            hosting_info = result.get('hosting_infrastructure', {}) or {}
            provider_info = hosting_info.get('provider', {}) or {}
            provider = provider_info.get('name', 'Unknown')
            
            # Get country from WHOIS
            whois_results = result.get('whois_results', [])
            country = whois_results[0].get('country', 'Unknown') if whois_results else 'Unknown'
            
            detailed_results.append({
                'exchange': self._extract_exchange_name(result.get('url', '')),
                'hostname': result.get('hostname', ''),
                'ip_addresses': ', '.join(all_ips[:3]),  # Show first 3 IPs
                'latency': f"{avg_latency:.2f}" if avg_latency else "N/A",
                'hops': hop_count,
                'provider': provider,
                'country': country
            })
        
        return {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_exchanges': summary_stats['total_exchanges'],
            'total_recommendations': len(recommendations),
            'avg_latency': f"{summary_stats['avg_latency']:.2f}",
            'best_latency': f"{summary_stats['best_latency']:.2f}",
            'worst_latency': f"{summary_stats['worst_latency']:.2f}",
            'unique_providers': summary_stats['unique_providers'],
            'top_recommendations': top_recommendations,
            'detailed_results': detailed_results
        }
    
    def _extract_exchange_name(self, url: str) -> str:
        """Extract exchange name from URL."""
        from urllib.parse import urlparse
        hostname = urlparse(url).hostname or url
        
        if 'binance' in hostname:
            return 'Binance'
        elif 'coinbase' in hostname:
            return 'Coinbase'
        elif 'kraken' in hostname:
            return 'Kraken'
        elif 'okx' in hostname or 'okex' in hostname:
            return 'OKX'
        elif 'huobi' in hostname:
            return 'Huobi'
        elif 'bybit' in hostname:
            return 'Bybit'
        elif 'kucoin' in hostname:
            return 'KuCoin'
        elif 'gateio' in hostname or 'gate.io' in hostname:
            return 'Gate.io'
        elif 'bitfinex' in hostname:
            return 'Bitfinex'
        elif 'bitstamp' in hostname:
            return 'Bitstamp'
        elif 'gemini' in hostname:
            return 'Gemini'
        elif 'deribit' in hostname:
            return 'Deribit'
        elif 'bitmex' in hostname:
            return 'BitMEX'
        elif 'crypto.com' in hostname:
            return 'Crypto.com'
        elif 'ftx' in hostname:
            return 'FTX'
        else:
            return hostname.split('.')[0].title()
