"""
Report Generation Module
Generates comprehensive reports in multiple formats (JSON, CSV, HTML).
"""

import json
import csv
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import asdict

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

try:
    from jinja2 import Template
    JINJA2_AVAILABLE = True
except ImportError:
    JINJA2_AVAILABLE = False

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

class ReportGenerator:
    """Generates comprehensive analysis reports in multiple formats."""
    
    def __init__(self, output_dir: str = "results"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def generate_all_reports(self, analysis_results: List[Dict[str, Any]], 
                           recommendations: List[Any],
                           config: Dict[str, Any] = None) -> Dict[str, str]:
        """Generate all report formats."""
        report_files = {}
        
        # JSON Report
        json_file = self.generate_json_report(analysis_results, recommendations)
        report_files['json'] = json_file
        
        # CSV Report
        csv_file = self.generate_csv_report(analysis_results, recommendations)
        report_files['csv'] = csv_file
        
        # HTML Report
        if JINJA2_AVAILABLE:
            html_file = self.generate_html_report(analysis_results, recommendations)
            report_files['html'] = html_file
        
        # Summary Report
        summary_file = self.generate_summary_report(analysis_results, recommendations)
        report_files['summary'] = summary_file
        
        return report_files
    
    def generate_json_report(self, analysis_results: List[Dict[str, Any]], 
                           recommendations: List[Any]) -> str:
        """Generate comprehensive JSON report."""
        filename = self.output_dir / f"crypto_connectivity_analysis_{self.timestamp}.json"
        
        # Convert recommendations to dict format
        recommendations_dict = []
        for rec in recommendations:
            if hasattr(rec, '__dict__'):
                recommendations_dict.append(asdict(rec))
            else:
                recommendations_dict.append(rec)
        
        report_data = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "version": "1.0",
                "total_exchanges_analyzed": len(analysis_results),
                "total_recommendations": len(recommendations)
            },
            "analysis_results": analysis_results,
            "recommendations": recommendations_dict,
            "summary": self._generate_summary_stats(analysis_results, recommendations)
        }
        
        with open(filename, 'w') as f:
            json.dump(report_data, f, indent=2, default=str)
        
        self.logger.info(f"JSON report generated: {filename}")
        return str(filename)
    
    def generate_csv_report(self, analysis_results: List[Dict[str, Any]], 
                          recommendations: List[Any]) -> str:
        """Generate CSV report with key metrics."""
        filename = self.output_dir / f"crypto_connectivity_summary_{self.timestamp}.csv"
        
        # Prepare data for CSV
        csv_data = []
        
        for result in analysis_results:
            if result.get('error'):
                continue
            
            # Extract key information
            url = result.get('url', '')
            hostname = result.get('hostname', '')
            
            # DNS info
            dns_info = result.get('dns', {})
            ipv4_addresses = ', '.join(dns_info.get('ipv4_addresses', []))
            ipv6_addresses = ', '.join(dns_info.get('ipv6_addresses', []))
            
            # Latency info from ping and traceroute
            ping_results = result.get('ping_results', [])
            traceroute_results = result.get('traceroute_results', [])
            min_latency = max_latency = avg_latency = hop_count = packet_loss = 0

            # Prefer ping results for latency (more reliable)
            if ping_results:
                successful_pings = [p for p in ping_results if p.get('success')]
                if successful_pings:
                    ping = successful_pings[0]  # Use first successful ping
                    min_latency = ping.get('min_rtt_ms', 0)
                    max_latency = ping.get('max_rtt_ms', 0)
                    avg_latency = ping.get('avg_rtt_ms', 0)
                    packet_loss = ping.get('packet_loss_percent', 0)

            # Fallback to traceroute for latency if ping failed
            if avg_latency == 0 and traceroute_results:
                all_rtts = []
                for tr in traceroute_results:
                    if tr.get('success') and tr.get('hops'):
                        for hop in tr['hops']:
                            if hop.get('rtt_ms'):
                                all_rtts.extend(hop['rtt_ms'])
                        hop_count = max(hop_count, len(tr['hops']))

                if all_rtts:
                    min_latency = min(all_rtts)
                    max_latency = max(all_rtts)
                    avg_latency = sum(all_rtts) / len(all_rtts)

            # Get hop count from traceroute
            if traceroute_results:
                for tr in traceroute_results:
                    if tr.get('success') and tr.get('hops'):
                        hop_count = max(hop_count, len(tr['hops']))
            
            # WHOIS info
            whois_results = result.get('whois_results', [])
            asn = organization = country = ''
            if whois_results:
                first_whois = whois_results[0]
                asn = first_whois.get('asn', '')
                organization = first_whois.get('organization', '')
                country = first_whois.get('country', '')
            
            # Hosting info
            hosting_info = result.get('hosting_infrastructure', {}) or {}
            provider_info = hosting_info.get('provider', {}) or {}
            datacenter_info = hosting_info.get('datacenter', {}) or {}
            provider = provider_info.get('name', '')
            datacenter = datacenter_info.get('name', '')
            
            csv_data.append({
                'Exchange': self._extract_exchange_name(url),
                'URL': url,
                'Hostname': hostname,
                'IPv4_Addresses': ipv4_addresses,
                'IPv6_Addresses': ipv6_addresses,
                'Min_Latency_ms': round(min_latency, 2),
                'Avg_Latency_ms': round(avg_latency, 2),
                'Max_Latency_ms': round(max_latency, 2),
                'Packet_Loss_Percent': round(packet_loss, 1),
                'Hop_Count': hop_count,
                'ASN': asn,
                'Organization': organization,
                'Country': country,
                'Hosting_Provider': provider,
                'Datacenter': datacenter
            })
        
        # Write CSV
        if csv_data:
            with open(filename, 'w', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=csv_data[0].keys())
                writer.writeheader()
                writer.writerows(csv_data)
        
        self.logger.info(f"CSV report generated: {filename}")
        return str(filename)
    
    def generate_html_report(self, analysis_results: List[Dict[str, Any]], 
                           recommendations: List[Any]) -> str:
        """Generate interactive HTML report."""
        filename = self.output_dir / f"crypto_connectivity_report_{self.timestamp}.html"
        
        # Enhanced HTML template with comprehensive data
        html_template = """
<!DOCTYPE html>
<html>
<head>
    <title>Cryptocurrency Exchange Connectivity Analysis</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .header h1 { margin: 0; font-size: 2.5em; }
        .header p { margin: 5px 0; opacity: 0.9; }
        .section { margin: 30px 0; }
        .section h2 {
            color: #333;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .metric {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        .metric-value { font-size: 2em; font-weight: bold; color: #667eea; }
        .metric-label { color: #666; margin-top: 5px; }
        .recommendation {
            background: #f9f9f9;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 5px solid #007cba;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .high-confidence { border-left-color: #28a745; }
        .medium-confidence { border-left-color: #ffc107; }
        .low-confidence { border-left-color: #dc3545; }
        .recommendation h3 { margin-top: 0; color: #333; }
        .recommendation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .recommendation-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #e0e0e0;
        }
        .recommendation-item strong { color: #667eea; }
        table {
            border-collapse: collapse;
            width: 100%;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        th {
            background: #667eea;
            color: white;
            padding: 15px 10px;
            text-align: left;
            font-weight: 600;
        }
        td {
            padding: 12px 10px;
            border-bottom: 1px solid #e0e0e0;
        }
        tr:hover { background-color: #f8f9fa; }
        .status-good { color: #28a745; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .collapsible {
            background-color: #f1f1f1;
            color: #444;
            cursor: pointer;
            padding: 15px;
            width: 100%;
            border: none;
            text-align: left;
            outline: none;
            font-size: 16px;
            border-radius: 5px;
            margin: 5px 0;
        }
        .collapsible:hover { background-color: #ddd; }
        .collapsible.active { background-color: #667eea; color: white; }
        .content {
            padding: 0 15px;
            display: none;
            overflow: hidden;
            background-color: #f9f9f9;
            border-radius: 0 0 5px 5px;
        }
        .content.show { display: block; padding: 15px; }
        .technical-details {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .ping-results, .traceroute-results, .whois-results {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e0e0e0;
        }
        .error { color: #dc3545; font-style: italic; }
        .success { color: #28a745; }
    </style>
    <script>
        function toggleContent(element) {
            element.classList.toggle("active");
            var content = element.nextElementSibling;
            content.classList.toggle("show");
        }
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Cryptocurrency Exchange Connectivity Analysis</h1>
            <p>📅 Generated on: {{ timestamp }}</p>
            <p>🔍 Total Exchanges Analyzed: {{ total_exchanges }}</p>
            <p>💡 Total Recommendations: {{ total_recommendations }}</p>
        </div>

        <div class="section">
            <h2>📊 Executive Summary</h2>
            <div class="metrics-grid">
                <div class="metric">
                    <div class="metric-value">{{ avg_latency }}ms</div>
                    <div class="metric-label">Average Latency</div>
                </div>
                <div class="metric">
                    <div class="metric-value">{{ best_latency }}ms</div>
                    <div class="metric-label">Best Latency</div>
                </div>
                <div class="metric">
                    <div class="metric-value">{{ worst_latency }}ms</div>
                    <div class="metric-label">Worst Latency</div>
                </div>
                <div class="metric">
                    <div class="metric-value">{{ unique_providers }}</div>
                    <div class="metric-label">Unique Providers</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 Top Connectivity Recommendations</h2>
            {% for rec in top_recommendations %}
            <div class="recommendation {{ rec.confidence_class }}">
                <h3>{{ rec.exchange_name }} - {{ rec.connection_type }}</h3>
                <div class="recommendation-grid">
                    <div class="recommendation-item">
                        <strong>Provider:</strong> {{ rec.provider }}
                    </div>
                    <div class="recommendation-item">
                        <strong>Datacenter:</strong> {{ rec.datacenter }}
                    </div>
                    <div class="recommendation-item">
                        <strong>Estimated Latency:</strong> {{ rec.estimated_latency }}
                    </div>
                    <div class="recommendation-item">
                        <strong>Monthly Cost:</strong> {{ rec.monthly_cost_range }}
                    </div>
                    <div class="recommendation-item">
                        <strong>Setup Time:</strong> {{ rec.setup_time }}
                    </div>
                    <div class="recommendation-item">
                        <strong>Confidence Score:</strong> {{ rec.confidence_score }}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <div class="section">
            <h2>📋 Exchange Summary Table</h2>
            <table>
                <thead>
                    <tr>
                        <th>Exchange</th>
                        <th>Hostname</th>
                        <th>IP Addresses</th>
                        <th>Avg Latency</th>
                        <th>Packet Loss</th>
                        <th>Hops</th>
                        <th>Provider</th>
                        <th>Country</th>
                        <th>ASN</th>
                    </tr>
                </thead>
                <tbody>
                    {% for result in detailed_results %}
                    <tr>
                        <td><strong>{{ result.exchange }}</strong></td>
                        <td>{{ result.hostname }}</td>
                        <td>{{ result.ip_addresses }}</td>
                        <td class="{% if result.latency_class %}{{ result.latency_class }}{% endif %}">
                            {{ result.latency }}
                        </td>
                        <td class="{% if result.packet_loss_class %}{{ result.packet_loss_class }}{% endif %}">
                            {{ result.packet_loss }}%
                        </td>
                        <td>{{ result.hops }}</td>
                        <td>{{ result.provider }}</td>
                        <td>{{ result.country }}</td>
                        <td>{{ result.asn }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🔍 Detailed Technical Analysis</h2>
            {% for detail in technical_details %}
            <button class="collapsible" onclick="toggleContent(this)">
                {{ detail.exchange }} - {{ detail.hostname }} ({{ detail.status }})
            </button>
            <div class="content">
                <div class="technical-details">
                    <h4>🌐 DNS Information</h4>
                    <p><strong>IPv4 Addresses:</strong> {{ detail.ipv4_addresses }}</p>
                    <p><strong>IPv6 Addresses:</strong> {{ detail.ipv6_addresses }}</p>
                    {% if detail.cname %}<p><strong>CNAME:</strong> {{ detail.cname }}</p>{% endif %}

                    <h4>🏓 Ping Results</h4>
                    {% for ping in detail.ping_results %}
                    <div class="ping-results">
                        <p><strong>Target:</strong> {{ ping.target }}</p>
                        {% if ping.success %}
                        <p class="success">✅ Ping successful</p>
                        <p><strong>Min/Avg/Max RTT:</strong> {{ ping.min_rtt_ms }}/{{ ping.avg_rtt_ms }}/{{ ping.max_rtt_ms }} ms</p>
                        <p><strong>Packet Loss:</strong> {{ ping.packet_loss_percent }}%</p>
                        <p><strong>Packets:</strong> {{ ping.packets_received }}/{{ ping.packets_sent }}</p>
                        {% else %}
                        <p class="error">❌ Ping failed: {{ ping.error }}</p>
                        {% endif %}
                    </div>
                    {% endfor %}

                    <h4>🛣️ Traceroute Results</h4>
                    {% for tr in detail.traceroute_results %}
                    <div class="traceroute-results">
                        <p><strong>Target:</strong> {{ tr.target_ip }}</p>
                        {% if tr.success %}
                        <p class="success">✅ Traceroute successful ({{ tr.total_hops }} hops)</p>
                        {% for hop in tr.hops %}
                        <p><strong>Hop {{ hop.hop_number }}:</strong> {{ hop.ip_address }} ({{ hop.avg_rtt_ms }}ms avg)</p>
                        {% endfor %}
                        {% else %}
                        <p class="error">❌ Traceroute failed: {{ tr.error }}</p>
                        {% endif %}
                    </div>
                    {% endfor %}

                    <h4>🏢 WHOIS Information</h4>
                    {% for whois in detail.whois_results %}
                    <div class="whois-results">
                        <p><strong>IP Address:</strong> {{ whois.ip_address }}</p>
                        {% if not whois.error %}
                        <p><strong>ASN:</strong> {{ whois.asn }}</p>
                        <p><strong>Organization:</strong> {{ whois.organization }}</p>
                        <p><strong>Country:</strong> {{ whois.country }}</p>
                        <p><strong>Network:</strong> {{ whois.network }}</p>
                        {% if whois.city %}<p><strong>City:</strong> {{ whois.city }}</p>{% endif %}
                        {% else %}
                        <p class="error">❌ WHOIS lookup failed: {{ whois.error }}</p>
                        {% endif %}
                    </div>
                    {% endfor %}

                    {% if detail.hosting_info %}
                    <h4>🏗️ Hosting Infrastructure</h4>
                    <div class="technical-details">
                        {% if detail.hosting_info.provider %}
                        <p><strong>Provider:</strong> {{ detail.hosting_info.provider.name }} ({{ detail.hosting_info.provider.type }})</p>
                        {% endif %}
                        {% if detail.hosting_info.datacenter %}
                        <p><strong>Datacenter:</strong> {{ detail.hosting_info.datacenter.name }}</p>
                        <p><strong>Location:</strong> {{ detail.hosting_info.datacenter.location }}</p>
                        <p><strong>City:</strong> {{ detail.hosting_info.datacenter.city }}, {{ detail.hosting_info.datacenter.country }}</p>
                        {% endif %}
                        <p><strong>Confidence:</strong> {{ detail.hosting_info.confidence }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</body>
</html>
        """
        
        # Prepare template data
        template_data = self._prepare_html_template_data(analysis_results, recommendations)
        
        if JINJA2_AVAILABLE:
            template = Template(html_template)
            html_content = template.render(**template_data)
            
            with open(filename, 'w') as f:
                f.write(html_content)
        
        self.logger.info(f"HTML report generated: {filename}")
        return str(filename)
    
    def generate_summary_report(self, analysis_results: List[Dict[str, Any]], 
                              recommendations: List[Any]) -> str:
        """Generate executive summary text report."""
        filename = self.output_dir / f"crypto_connectivity_summary_{self.timestamp}.txt"
        
        summary_stats = self._generate_summary_stats(analysis_results, recommendations)
        
        with open(filename, 'w') as f:
            f.write("CRYPTOCURRENCY EXCHANGE CONNECTIVITY ANALYSIS\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("EXECUTIVE SUMMARY\n")
            f.write("-" * 20 + "\n")
            f.write(f"Total Exchanges Analyzed: {summary_stats['total_exchanges']}\n")
            f.write(f"Successful Analyses: {summary_stats['successful_analyses']}\n")
            f.write(f"Failed Analyses: {summary_stats['failed_analyses']}\n")
            f.write(f"Average Latency: {summary_stats['avg_latency']:.2f}ms\n")
            f.write(f"Best Latency: {summary_stats['best_latency']:.2f}ms\n")
            f.write(f"Worst Latency: {summary_stats['worst_latency']:.2f}ms\n\n")
            
            f.write("TOP RECOMMENDATIONS\n")
            f.write("-" * 20 + "\n")
            
            # Sort recommendations by confidence score
            sorted_recs = sorted(recommendations, 
                               key=lambda x: getattr(x, 'confidence_score', 0), 
                               reverse=True)
            
            for i, rec in enumerate(sorted_recs[:5], 1):
                f.write(f"{i}. {rec.exchange_name}\n")
                f.write(f"   Provider: {rec.provider}\n")
                f.write(f"   Connection: {rec.connection_type}\n")
                f.write(f"   Latency: {rec.estimated_latency}\n")
                f.write(f"   Cost: {rec.monthly_cost_range}\n")
                f.write(f"   Confidence: {rec.confidence_score:.2f}\n\n")
            
            f.write("PROVIDER DISTRIBUTION\n")
            f.write("-" * 20 + "\n")
            provider_counts = summary_stats.get('provider_distribution', {})
            for provider, count in provider_counts.items():
                f.write(f"{provider}: {count} exchanges\n")
        
        self.logger.info(f"Summary report generated: {filename}")
        return str(filename)
    
    def _generate_summary_stats(self, analysis_results: List[Dict[str, Any]], 
                              recommendations: List[Any]) -> Dict[str, Any]:
        """Generate summary statistics."""
        total_exchanges = len(analysis_results)
        successful_analyses = len([r for r in analysis_results if not r.get('error')])
        failed_analyses = total_exchanges - successful_analyses
        
        # Calculate latency statistics
        all_latencies = []
        provider_counts = {}

        for result in analysis_results:
            if result.get('error'):
                continue

            # Extract latencies from ping results (preferred)
            ping_results = result.get('ping_results', [])
            for ping in ping_results:
                if ping.get('success') and ping.get('avg_rtt_ms', 0) > 0:
                    all_latencies.append(ping['avg_rtt_ms'])

            # Fallback to traceroute latencies if no ping data
            if not all_latencies:
                traceroute_results = result.get('traceroute_results', [])
                for tr in traceroute_results:
                    if tr.get('success') and tr.get('hops'):
                        for hop in tr['hops']:
                            if hop.get('rtt_ms'):
                                all_latencies.extend(hop['rtt_ms'])
            
            # Count providers
            hosting_info = result.get('hosting_infrastructure', {}) or {}
            provider_info = hosting_info.get('provider', {}) or {}
            provider = provider_info.get('name', 'Unknown')
            provider_counts[provider] = provider_counts.get(provider, 0) + 1
        
        avg_latency = sum(all_latencies) / len(all_latencies) if all_latencies else 0
        best_latency = min(all_latencies) if all_latencies else 0
        worst_latency = max(all_latencies) if all_latencies else 0
        
        return {
            'total_exchanges': total_exchanges,
            'successful_analyses': successful_analyses,
            'failed_analyses': failed_analyses,
            'avg_latency': avg_latency,
            'best_latency': best_latency,
            'worst_latency': worst_latency,
            'provider_distribution': provider_counts,
            'unique_providers': len(provider_counts)
        }
    
    def _prepare_html_template_data(self, analysis_results: List[Dict[str, Any]], 
                                  recommendations: List[Any]) -> Dict[str, Any]:
        """Prepare data for HTML template."""
        summary_stats = self._generate_summary_stats(analysis_results, recommendations)
        
        # Prepare top recommendations
        top_recommendations = []
        sorted_recs = sorted(recommendations, 
                           key=lambda x: getattr(x, 'confidence_score', 0), 
                           reverse=True)
        
        for rec in sorted_recs[:5]:
            confidence_class = "high-confidence" if rec.confidence_score > 0.8 else \
                             "medium-confidence" if rec.confidence_score > 0.5 else \
                             "low-confidence"
            
            top_recommendations.append({
                'exchange_name': rec.exchange_name,
                'provider': rec.provider,
                'datacenter': rec.datacenter,
                'connection_type': rec.connection_type,
                'estimated_latency': rec.estimated_latency,
                'monthly_cost_range': rec.monthly_cost_range,
                'setup_time': rec.setup_time,
                'confidence_score': f"{rec.confidence_score:.2f}",
                'confidence_class': confidence_class
            })
        
        # Prepare detailed results
        detailed_results = []
        technical_details = []

        for result in analysis_results:
            if result.get('error'):
                continue

            dns_info = result.get('dns', {})
            ping_results = result.get('ping_results', [])
            traceroute_results = result.get('traceroute_results', [])
            whois_results = result.get('whois_results', [])
            hosting_info = result.get('hosting_infrastructure', {}) or {}

            all_ips = dns_info.get('ipv4_addresses', []) + dns_info.get('ipv6_addresses', [])

            # Calculate latency from ping results (preferred) or traceroute
            avg_latency = packet_loss = hop_count = 0

            # Use ping results for latency
            if ping_results:
                successful_pings = [p for p in ping_results if p.get('success')]
                if successful_pings:
                    ping = successful_pings[0]
                    avg_latency = ping.get('avg_rtt_ms', 0)
                    packet_loss = ping.get('packet_loss_percent', 0)

            # Fallback to traceroute for latency
            if avg_latency == 0 and traceroute_results:
                all_rtts = []
                for tr in traceroute_results:
                    if tr.get('success') and tr.get('hops'):
                        for hop in tr['hops']:
                            if hop.get('rtt_ms'):
                                all_rtts.extend(hop['rtt_ms'])
                        hop_count = max(hop_count, len(tr['hops']))

                if all_rtts:
                    avg_latency = sum(all_rtts) / len(all_rtts)

            # Get hop count from traceroute
            if traceroute_results:
                for tr in traceroute_results:
                    if tr.get('success') and tr.get('hops'):
                        hop_count = max(hop_count, len(tr['hops']))

            # Get provider and country info
            provider_info = hosting_info.get('provider', {}) or {}
            provider = provider_info.get('name', 'Unknown')

            asn = whois_results[0].get('asn', 'Unknown') if whois_results else 'Unknown'
            country = whois_results[0].get('country', 'Unknown') if whois_results else 'Unknown'

            # Determine status classes for styling
            latency_class = ""
            if avg_latency > 0:
                if avg_latency < 10:
                    latency_class = "status-good"
                elif avg_latency < 50:
                    latency_class = "status-warning"
                else:
                    latency_class = "status-error"

            packet_loss_class = ""
            if packet_loss == 0:
                packet_loss_class = "status-good"
            elif packet_loss < 5:
                packet_loss_class = "status-warning"
            else:
                packet_loss_class = "status-error"

            detailed_results.append({
                'exchange': self._extract_exchange_name(result.get('url', '')),
                'hostname': result.get('hostname', ''),
                'ip_addresses': ', '.join(all_ips[:3]),  # Show first 3 IPs
                'latency': f"{avg_latency:.2f}" if avg_latency else "N/A",
                'latency_class': latency_class,
                'packet_loss': f"{packet_loss:.1f}" if packet_loss >= 0 else "N/A",
                'packet_loss_class': packet_loss_class,
                'hops': hop_count,
                'provider': provider,
                'country': country,
                'asn': asn
            })

            # Prepare technical details for collapsible sections
            status = "✅ Success" if not result.get('error') else "❌ Error"
            if ping_results and any(p.get('success') for p in ping_results):
                status = "✅ Success"
            elif traceroute_results and any(tr.get('success') for tr in traceroute_results):
                status = "⚠️ Partial"
            else:
                status = "❌ Failed"

            technical_details.append({
                'exchange': self._extract_exchange_name(result.get('url', '')),
                'hostname': result.get('hostname', ''),
                'status': status,
                'ipv4_addresses': ', '.join(dns_info.get('ipv4_addresses', [])),
                'ipv6_addresses': ', '.join(dns_info.get('ipv6_addresses', [])),
                'cname': dns_info.get('cname'),
                'ping_results': ping_results,
                'traceroute_results': traceroute_results,
                'whois_results': whois_results,
                'hosting_info': hosting_info
            })
        
        return {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_exchanges': summary_stats['total_exchanges'],
            'total_recommendations': len(recommendations),
            'avg_latency': f"{summary_stats['avg_latency']:.2f}",
            'best_latency': f"{summary_stats['best_latency']:.2f}",
            'worst_latency': f"{summary_stats['worst_latency']:.2f}",
            'unique_providers': summary_stats['unique_providers'],
            'top_recommendations': top_recommendations,
            'detailed_results': detailed_results,
            'technical_details': technical_details
        }
    
    def _extract_exchange_name(self, url: str) -> str:
        """Extract exchange name from URL."""
        from urllib.parse import urlparse
        hostname = urlparse(url).hostname or url
        
        if 'binance' in hostname:
            return 'Binance'
        elif 'coinbase' in hostname:
            return 'Coinbase'
        elif 'kraken' in hostname:
            return 'Kraken'
        elif 'okx' in hostname or 'okex' in hostname:
            return 'OKX'
        elif 'huobi' in hostname:
            return 'Huobi'
        elif 'bybit' in hostname:
            return 'Bybit'
        elif 'kucoin' in hostname:
            return 'KuCoin'
        elif 'gateio' in hostname or 'gate.io' in hostname:
            return 'Gate.io'
        elif 'bitfinex' in hostname:
            return 'Bitfinex'
        elif 'bitstamp' in hostname:
            return 'Bitstamp'
        elif 'gemini' in hostname:
            return 'Gemini'
        elif 'deribit' in hostname:
            return 'Deribit'
        elif 'bitmex' in hostname:
            return 'BitMEX'
        elif 'crypto.com' in hostname:
            return 'Crypto.com'
        elif 'ftx' in hostname:
            return 'FTX'
        else:
            return hostname.split('.')[0].title()
