"""
Network Analysis Module for Cryptocurrency Exchange Connectivity
Handles DNS resolution, traceroute analysis, and WHOIS lookups.
"""

import asyncio
import socket
import subprocess
import json
import logging
import re
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from urllib.parse import urlparse
import ipaddress

try:
    import dns.resolver
    import dns.exception
    DNS_AVAILABLE = True
except ImportError:
    DNS_AVAILABLE = False

try:
    from ipwhois import IPWhois
    IPWHOIS_AVAILABLE = True
except ImportError:
    IPWHOIS_AVAILABLE = False

@dataclass
class DNSResult:
    """DNS resolution result."""
    hostname: str
    ipv4_addresses: List[str]
    ipv6_addresses: List[str]
    cname: Optional[str] = None
    ttl: Optional[int] = None
    error: Optional[str] = None

@dataclass
class TracerouteHop:
    """Single hop in traceroute."""
    hop_number: int
    ip_address: str
    hostname: Optional[str]
    rtt_ms: List[float]
    avg_rtt_ms: float

@dataclass
class TracerouteResult:
    """Traceroute analysis result."""
    target: str
    target_ip: str
    hops: List[TracerouteHop]
    total_hops: int
    success: bool
    error: Optional[str] = None

@dataclass
class WhoisResult:
    """WHOIS lookup result."""
    ip_address: str
    asn: Optional[str] = None
    asn_description: Optional[str] = None
    network: Optional[str] = None
    country: Optional[str] = None
    city: Optional[str] = None
    organization: Optional[str] = None
    isp: Optional[str] = None
    error: Optional[str] = None

class NetworkAnalyzer:
    """Network analysis utilities for exchange connectivity."""
    
    def __init__(self, timeout: int = 30, max_retries: int = 3):
        self.timeout = timeout
        self.max_retries = max_retries
        self.logger = logging.getLogger(__name__)
    
    def extract_hostname(self, websocket_url: str) -> str:
        """Extract hostname from websocket URL."""
        parsed = urlparse(websocket_url)
        return parsed.hostname or parsed.netloc.split(':')[0]
    
    async def resolve_dns(self, hostname: str) -> DNSResult:
        """Resolve DNS for hostname with both IPv4 and IPv6."""
        result = DNSResult(hostname=hostname, ipv4_addresses=[], ipv6_addresses=[])
        
        try:
            # Try IPv4 resolution
            try:
                ipv4_info = socket.getaddrinfo(hostname, None, socket.AF_INET)
                result.ipv4_addresses = list(set([info[4][0] for info in ipv4_info]))
            except socket.gaierror as e:
                self.logger.debug(f"IPv4 resolution failed for {hostname}: {e}")
            
            # Try IPv6 resolution
            try:
                ipv6_info = socket.getaddrinfo(hostname, None, socket.AF_INET6)
                result.ipv6_addresses = list(set([info[4][0] for info in ipv6_info]))
            except socket.gaierror as e:
                self.logger.debug(f"IPv6 resolution failed for {hostname}: {e}")
            
            # Enhanced DNS resolution with dnspython if available
            if DNS_AVAILABLE:
                try:
                    # Get CNAME
                    try:
                        cname_answers = dns.resolver.resolve(hostname, 'CNAME')
                        result.cname = str(cname_answers[0])
                    except (dns.resolver.NXDOMAIN, dns.resolver.NoAnswer):
                        pass
                    
                    # Get TTL from A record
                    try:
                        a_answers = dns.resolver.resolve(hostname, 'A')
                        result.ttl = a_answers.rrset.ttl
                    except (dns.resolver.NXDOMAIN, dns.resolver.NoAnswer):
                        pass
                        
                except Exception as e:
                    self.logger.debug(f"Enhanced DNS lookup failed for {hostname}: {e}")
            
            if not result.ipv4_addresses and not result.ipv6_addresses:
                result.error = "No IP addresses resolved"
                
        except Exception as e:
            result.error = str(e)
            self.logger.error(f"DNS resolution failed for {hostname}: {e}")
        
        return result
    
    async def run_ping_test(self, target_ip: str, count: int = 5) -> Dict[str, Any]:
        """Run ping test to measure latency."""
        result = {
            'target': target_ip,
            'packets_sent': count,
            'packets_received': 0,
            'packet_loss_percent': 100.0,
            'min_rtt_ms': 0.0,
            'avg_rtt_ms': 0.0,
            'max_rtt_ms': 0.0,
            'success': False,
            'error': None
        }

        try:
            # Use ping command
            if self._is_ipv6(target_ip):
                cmd = ['ping6', '-c', str(count), '-W', '3', target_ip]
            else:
                cmd = ['ping', '-c', str(count), '-W', '3', target_ip]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=self.timeout
            )

            if process.returncode == 0:
                ping_data = self._parse_ping_output(stdout.decode())
                result.update(ping_data)
                result['success'] = True
            else:
                result['error'] = stderr.decode().strip()

        except asyncio.TimeoutError:
            result['error'] = f"Ping timeout after {self.timeout} seconds"
        except FileNotFoundError:
            result['error'] = "Ping command not found"
        except Exception as e:
            result['error'] = str(e)
            self.logger.error(f"Ping failed for {target_ip}: {e}")

        return result

    def _parse_ping_output(self, output: str) -> Dict[str, Any]:
        """Parse ping command output."""
        import re

        result = {
            'packets_received': 0,
            'packet_loss_percent': 100.0,
            'min_rtt_ms': 0.0,
            'avg_rtt_ms': 0.0,
            'max_rtt_ms': 0.0
        }

        # Parse packet loss
        loss_match = re.search(r'(\d+)% packet loss', output)
        if loss_match:
            result['packet_loss_percent'] = float(loss_match.group(1))
            result['packets_received'] = int(5 * (100 - result['packet_loss_percent']) / 100)

        # Parse RTT statistics
        rtt_match = re.search(r'min/avg/max/mdev = ([\d.]+)/([\d.]+)/([\d.]+)/([\d.]+) ms', output)
        if rtt_match:
            result['min_rtt_ms'] = float(rtt_match.group(1))
            result['avg_rtt_ms'] = float(rtt_match.group(2))
            result['max_rtt_ms'] = float(rtt_match.group(3))
        else:
            # Try alternative format (macOS)
            rtt_match = re.search(r'min/avg/max/stddev = ([\d.]+)/([\d.]+)/([\d.]+)/([\d.]+) ms', output)
            if rtt_match:
                result['min_rtt_ms'] = float(rtt_match.group(1))
                result['avg_rtt_ms'] = float(rtt_match.group(2))
                result['max_rtt_ms'] = float(rtt_match.group(3))

        return result

    async def run_traceroute(self, target_ip: str, max_hops: int = 30) -> TracerouteResult:
        """Run traceroute to target IP address."""
        result = TracerouteResult(
            target=target_ip,
            target_ip=target_ip,
            hops=[],
            total_hops=0,
            success=False
        )

        try:
            # Determine the appropriate traceroute command
            if self._is_ipv6(target_ip):
                cmd = ['traceroute6', '-n', '-m', str(max_hops), '-w', '3', target_ip]
            else:
                cmd = ['traceroute', '-n', '-m', str(max_hops), '-w', '3', target_ip]

            # Run traceroute
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=self.timeout
            )

            if process.returncode == 0:
                result.hops = self._parse_traceroute_output(stdout.decode())
                result.total_hops = len(result.hops)
                result.success = True
            else:
                result.error = stderr.decode().strip()

        except asyncio.TimeoutError:
            result.error = f"Traceroute timeout after {self.timeout} seconds"
        except FileNotFoundError:
            # Try alternative traceroute commands
            result = await self._try_alternative_traceroute(target_ip, max_hops)
        except Exception as e:
            result.error = str(e)
            self.logger.error(f"Traceroute failed for {target_ip}: {e}")

        return result
    
    async def _try_alternative_traceroute(self, target_ip: str, max_hops: int) -> TracerouteResult:
        """Try alternative traceroute commands."""
        result = TracerouteResult(
            target=target_ip,
            target_ip=target_ip,
            hops=[],
            total_hops=0,
            success=False
        )
        
        # Try mtr if available
        try:
            cmd = ['mtr', '--report', '--report-cycles', '3', '--no-dns', target_ip]
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=self.timeout
            )
            
            if process.returncode == 0:
                result.hops = self._parse_mtr_output(stdout.decode())
                result.total_hops = len(result.hops)
                result.success = True
                return result
                
        except (FileNotFoundError, asyncio.TimeoutError):
            pass
        
        # Fallback to ping-based path discovery
        result.error = "No traceroute tools available"
        return result
    
    def _parse_traceroute_output(self, output: str) -> List[TracerouteHop]:
        """Parse traceroute command output."""
        hops = []
        lines = output.strip().split('\n')[1:]  # Skip header
        
        for line in lines:
            if not line.strip():
                continue
                
            # Parse traceroute line format
            parts = line.strip().split()
            if len(parts) < 2:
                continue
            
            try:
                hop_num = int(parts[0])
                
                # Extract IP and timing info
                ip_match = re.search(r'(\d+\.\d+\.\d+\.\d+)', line)
                if not ip_match:
                    continue
                
                ip_addr = ip_match.group(1)
                
                # Extract RTT values
                rtt_values = []
                for part in parts[2:]:
                    if part.endswith('ms'):
                        try:
                            rtt_values.append(float(part[:-2]))
                        except ValueError:
                            continue
                
                if rtt_values:
                    avg_rtt = sum(rtt_values) / len(rtt_values)
                    hop = TracerouteHop(
                        hop_number=hop_num,
                        ip_address=ip_addr,
                        hostname=None,
                        rtt_ms=rtt_values,
                        avg_rtt_ms=avg_rtt
                    )
                    hops.append(hop)
                    
            except (ValueError, IndexError):
                continue
        
        return hops
    
    def _parse_mtr_output(self, output: str) -> List[TracerouteHop]:
        """Parse MTR command output."""
        hops = []
        lines = output.strip().split('\n')
        
        for line in lines:
            if not line.strip() or 'HOST:' in line or 'Start:' in line:
                continue
            
            # MTR format: hop. hostname/IP loss% snt last avg best wrst stdev
            parts = line.strip().split()
            if len(parts) < 6:
                continue
            
            try:
                hop_num = int(parts[0].rstrip('.'))
                host_info = parts[1]
                
                # Extract IP from hostname/IP format
                ip_match = re.search(r'(\d+\.\d+\.\d+\.\d+)', host_info)
                if not ip_match:
                    continue
                
                ip_addr = ip_match.group(1)
                avg_rtt = float(parts[5])  # avg column
                
                hop = TracerouteHop(
                    hop_number=hop_num,
                    ip_address=ip_addr,
                    hostname=None,
                    rtt_ms=[avg_rtt],
                    avg_rtt_ms=avg_rtt
                )
                hops.append(hop)
                
            except (ValueError, IndexError):
                continue
        
        return hops
    
    async def whois_lookup(self, ip_address: str) -> WhoisResult:
        """Perform WHOIS lookup for IP address."""
        result = WhoisResult(ip_address=ip_address)
        
        if not IPWHOIS_AVAILABLE:
            result.error = "ipwhois library not available"
            return result
        
        try:
            whois = IPWhois(ip_address)
            whois_data = whois.lookup_rdap(depth=1)
            
            # Extract relevant information
            if 'asn' in whois_data:
                result.asn = whois_data['asn']
            
            if 'asn_description' in whois_data:
                result.asn_description = whois_data['asn_description']
            
            if 'network' in whois_data and whois_data['network']:
                network_info = whois_data['network']
                result.network = network_info.get('cidr')
                result.country = network_info.get('country')
                
            # Try to extract organization info
            if 'objects' in whois_data:
                for obj_key, obj_data in whois_data['objects'].items():
                    if 'contact' in obj_data and obj_data['contact']:
                        contact = obj_data['contact']
                        if 'name' in contact:
                            result.organization = contact['name']
                            break
            
        except Exception as e:
            result.error = str(e)
            self.logger.error(f"WHOIS lookup failed for {ip_address}: {e}")
        
        return result
    
    def _is_ipv6(self, ip_str: str) -> bool:
        """Check if IP address is IPv6."""
        try:
            return isinstance(ipaddress.ip_address(ip_str), ipaddress.IPv6Address)
        except ValueError:
            return False
    
    async def analyze_endpoint(self, websocket_url: str) -> Dict[str, Any]:
        """Perform complete network analysis for a websocket endpoint."""
        hostname = self.extract_hostname(websocket_url)

        # DNS resolution
        dns_result = await self.resolve_dns(hostname)

        analysis = {
            'url': websocket_url,
            'hostname': hostname,
            'dns': asdict(dns_result),
            'ping_results': [],
            'traceroute_results': [],
            'whois_results': []
        }

        # Analyze each resolved IP
        all_ips = dns_result.ipv4_addresses + dns_result.ipv6_addresses

        for ip in all_ips[:3]:  # Limit to first 3 IPs to avoid excessive requests
            # Ping test for latency
            ping_result = await self.run_ping_test(ip)
            analysis['ping_results'].append(ping_result)

            # Traceroute (may fail due to ICMP blocking)
            traceroute_result = await self.run_traceroute(ip)
            analysis['traceroute_results'].append(asdict(traceroute_result))

            # WHOIS
            whois_result = await self.whois_lookup(ip)
            analysis['whois_results'].append(asdict(whois_result))

        return analysis

    async def batch_analyze(self, websocket_urls: List[str],
                          concurrent_limit: int = 5) -> List[Dict[str, Any]]:
        """Analyze multiple endpoints concurrently with rate limiting."""
        semaphore = asyncio.Semaphore(concurrent_limit)

        async def analyze_with_semaphore(url: str) -> Dict[str, Any]:
            async with semaphore:
                try:
                    return await self.analyze_endpoint(url)
                except Exception as e:
                    self.logger.error(f"Analysis failed for {url}: {e}")
                    return {
                        'url': url,
                        'error': str(e),
                        'dns': None,
                        'traceroute_results': [],
                        'whois_results': []
                    }

        tasks = [analyze_with_semaphore(url) for url in websocket_urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Handle any exceptions that weren't caught
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    'url': websocket_urls[i],
                    'error': str(result),
                    'dns': None,
                    'traceroute_results': [],
                    'whois_results': []
                })
            else:
                processed_results.append(result)

        return processed_results
