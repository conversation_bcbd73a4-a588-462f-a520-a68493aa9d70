#!/usr/bin/env python3
"""
Test script for the Cryptocurrency Exchange Connectivity Analyzer
Verifies basic functionality and dependencies.
"""

import asyncio
import sys
import logging
from pathlib import Path

# Test imports
def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    
    try:
        from exchange_endpoints import ExchangeEndpoints
        print("✓ exchange_endpoints imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import exchange_endpoints: {e}")
        return False
    
    try:
        from network_analyzer import NetworkAnalyzer
        print("✓ network_analyzer imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import network_analyzer: {e}")
        return False
    
    try:
        from datacenter_mapper import DatacenterMapper
        print("✓ datacenter_mapper imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import datacenter_mapper: {e}")
        return False
    
    try:
        from connectivity_recommender import ConnectivityRecommender
        print("✓ connectivity_recommender imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import connectivity_recommender: {e}")
        return False
    
    try:
        from report_generator import ReportGenerator
        print("✓ report_generator imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import report_generator: {e}")
        return False
    
    try:
        from crypto_connectivity_analyzer import CryptoConnectivityAnalyzer
        print("✓ crypto_connectivity_analyzer imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import crypto_connectivity_analyzer: {e}")
        return False
    
    return True

def test_exchange_endpoints():
    """Test exchange endpoints functionality."""
    print("\nTesting exchange endpoints...")
    
    try:
        from exchange_endpoints import ExchangeEndpoints
        
        endpoints = ExchangeEndpoints()
        all_endpoints = endpoints.get_all_endpoints()
        
        print(f"✓ Loaded {len(all_endpoints)} exchange endpoints")
        
        # Test specific exchange lookup
        binance = endpoints.get_by_name("binance")
        if binance:
            print(f"✓ Found Binance endpoint: {binance.url}")
        else:
            print("✗ Could not find Binance endpoint")
            return False
        
        # Test L3 endpoints
        l3_endpoints = endpoints.get_l3_endpoints()
        print(f"✓ Found {len(l3_endpoints)} L3 order book endpoints")
        
        return True
        
    except Exception as e:
        print(f"✗ Exchange endpoints test failed: {e}")
        return False

async def test_network_analyzer():
    """Test network analyzer functionality."""
    print("\nTesting network analyzer...")
    
    try:
        from network_analyzer import NetworkAnalyzer
        
        analyzer = NetworkAnalyzer(timeout=10)
        
        # Test DNS resolution
        test_hostname = "www.google.com"
        dns_result = await analyzer.resolve_dns(test_hostname)
        
        if dns_result.ipv4_addresses:
            print(f"✓ DNS resolution successful for {test_hostname}")
            print(f"  IPv4: {dns_result.ipv4_addresses[0]}")
        else:
            print(f"✗ DNS resolution failed for {test_hostname}")
            return False
        
        # Test hostname extraction
        test_url = "wss://stream.binance.com:9443/ws"
        hostname = analyzer.extract_hostname(test_url)
        print(f"✓ Hostname extraction: {test_url} -> {hostname}")
        
        return True
        
    except Exception as e:
        print(f"✗ Network analyzer test failed: {e}")
        return False

def test_datacenter_mapper():
    """Test datacenter mapper functionality."""
    print("\nTesting datacenter mapper...")
    
    try:
        from datacenter_mapper import DatacenterMapper
        
        mapper = DatacenterMapper()
        
        # Test provider identification by ASN
        aws_provider = mapper.identify_provider_by_asn("AS16509")
        if aws_provider:
            print(f"✓ ASN lookup successful: AS16509 -> {aws_provider.name}")
        else:
            print("✗ ASN lookup failed for AWS")
            return False
        
        # Test provider identification by IP
        aws_ip_provider = mapper.identify_provider_by_ip("********")
        if aws_ip_provider:
            print(f"✓ IP lookup successful: ******** -> {aws_ip_provider.name}")
        else:
            print("✗ IP lookup failed for AWS IP")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Datacenter mapper test failed: {e}")
        return False

def test_connectivity_recommender():
    """Test connectivity recommender functionality."""
    print("\nTesting connectivity recommender...")
    
    try:
        from connectivity_recommender import ConnectivityRecommender, LatencyAnalysis
        
        recommender = ConnectivityRecommender()
        
        # Test latency analysis
        mock_traceroute = [{
            'success': True,
            'hops': [
                {'hop_number': 1, 'rtt_ms': [1.2, 1.1, 1.3], 'avg_rtt_ms': 1.2},
                {'hop_number': 2, 'rtt_ms': [5.5, 5.7, 5.3], 'avg_rtt_ms': 5.5},
                {'hop_number': 3, 'rtt_ms': [12.1, 12.3, 11.9], 'avg_rtt_ms': 12.1}
            ]
        }]
        
        latency_analysis = recommender.analyze_latency(mock_traceroute)
        print(f"✓ Latency analysis: avg={latency_analysis.avg_latency_ms:.2f}ms, hops={latency_analysis.hop_count}")
        
        return True
        
    except Exception as e:
        print(f"✗ Connectivity recommender test failed: {e}")
        return False

def test_report_generator():
    """Test report generator functionality."""
    print("\nTesting report generator...")
    
    try:
        from report_generator import ReportGenerator
        
        # Create test output directory
        test_dir = Path("test_output")
        test_dir.mkdir(exist_ok=True)
        
        generator = ReportGenerator(output_dir=str(test_dir))
        
        # Test with minimal data
        mock_analysis = [{
            'url': 'wss://test.example.com/ws',
            'hostname': 'test.example.com',
            'dns': {'ipv4_addresses': ['*******'], 'ipv6_addresses': []},
            'traceroute_results': [],
            'whois_results': []
        }]
        
        mock_recommendations = []
        
        # Generate JSON report
        json_file = generator.generate_json_report(mock_analysis, mock_recommendations)
        if Path(json_file).exists():
            print(f"✓ JSON report generated: {json_file}")
        else:
            print("✗ JSON report generation failed")
            return False
        
        # Cleanup
        Path(json_file).unlink()
        test_dir.rmdir()
        
        return True
        
    except Exception as e:
        print(f"✗ Report generator test failed: {e}")
        return False

def test_dependencies():
    """Test optional dependencies."""
    print("\nTesting optional dependencies...")
    
    # Test DNS python
    try:
        import dns.resolver
        print("✓ dnspython available")
    except ImportError:
        print("⚠ dnspython not available (DNS resolution will use basic methods)")
    
    # Test ipwhois
    try:
        from ipwhois import IPWhois
        print("✓ ipwhois available")
    except ImportError:
        print("⚠ ipwhois not available (WHOIS lookups will be limited)")
    
    # Test pandas
    try:
        import pandas as pd
        print("✓ pandas available")
    except ImportError:
        print("⚠ pandas not available (some report features may be limited)")
    
    # Test jinja2
    try:
        from jinja2 import Template
        print("✓ jinja2 available")
    except ImportError:
        print("⚠ jinja2 not available (HTML reports will not be generated)")
    
    # Test plotly
    try:
        import plotly.graph_objects as go
        print("✓ plotly available")
    except ImportError:
        print("⚠ plotly not available (visualizations will not be generated)")
    
    return True

async def run_basic_analysis():
    """Run a basic analysis on a single exchange."""
    print("\nRunning basic analysis test...")
    
    try:
        from crypto_connectivity_analyzer import CryptoConnectivityAnalyzer
        
        # Create analyzer with minimal config
        analyzer = CryptoConnectivityAnalyzer()
        
        # Test with just one exchange
        analysis_results = await analyzer.analyze_all_exchanges(['binance'])
        
        if analysis_results and len(analysis_results) > 0:
            result = analysis_results[0]
            if not result.get('error'):
                print("✓ Basic analysis completed successfully")
                print(f"  Exchange: {result.get('hostname', 'Unknown')}")
                
                dns_info = result.get('dns', {})
                if dns_info.get('ipv4_addresses'):
                    print(f"  IP: {dns_info['ipv4_addresses'][0]}")
                
                return True
            else:
                print(f"✗ Analysis failed: {result.get('error')}")
                return False
        else:
            print("✗ No analysis results returned")
            return False
        
    except Exception as e:
        print(f"✗ Basic analysis test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("Cryptocurrency Exchange Connectivity Analyzer - Test Suite")
    print("=" * 60)
    
    # Suppress logging during tests
    logging.getLogger().setLevel(logging.ERROR)
    
    tests = [
        ("Import Tests", test_imports),
        ("Exchange Endpoints", test_exchange_endpoints),
        ("Network Analyzer", test_network_analyzer),
        ("Datacenter Mapper", test_datacenter_mapper),
        ("Connectivity Recommender", test_connectivity_recommender),
        ("Report Generator", test_report_generator),
        ("Dependencies", test_dependencies),
        ("Basic Analysis", run_basic_analysis)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
                
        except Exception as e:
            print(f"✗ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The analyzer is ready to use.")
        return 0
    else:
        print("⚠ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
