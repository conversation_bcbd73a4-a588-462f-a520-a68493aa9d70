"""
Datacenter Mapping and Identification Module
Maps IP addresses and network information to specific datacenters and hosting providers.
"""

import json
import logging
import re
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict

@dataclass
class DatacenterInfo:
    """Information about a datacenter facility."""
    name: str
    provider: str
    location: str
    city: str
    country: str
    region: str
    facility_code: Optional[str] = None
    address: Optional[str] = None
    connectivity_options: List[str] = None
    
    def __post_init__(self):
        if self.connectivity_options is None:
            self.connectivity_options = []

@dataclass
class HostingProvider:
    """Information about a hosting/cloud provider."""
    name: str
    type: str  # cloud, colo, cdn, etc.
    asn_ranges: List[str]
    ip_ranges: List[str]
    datacenters: List[DatacenterInfo]
    website: Optional[str] = None

class DatacenterMapper:
    """Maps network information to datacenters and hosting providers."""
    
    def __init__(self, custom_mapping_file: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        self.providers = self._load_provider_database()
        self.asn_to_provider = self._build_asn_mapping()
        
        if custom_mapping_file:
            self._load_custom_mapping(custom_mapping_file)
    
    def _load_provider_database(self) -> Dict[str, HostingProvider]:
        """Load the built-in provider and datacenter database."""
        providers = {}
        
        # AWS
        aws_datacenters = [
            DatacenterInfo("us-east-1", "AWS", "N. Virginia", "Ashburn", "US", "North America", "IAD"),
            DatacenterInfo("us-west-2", "AWS", "Oregon", "Portland", "US", "North America", "PDX"),
            DatacenterInfo("eu-west-1", "AWS", "Ireland", "Dublin", "IE", "Europe", "DUB"),
            DatacenterInfo("ap-southeast-1", "AWS", "Singapore", "Singapore", "SG", "Asia Pacific", "SIN"),
            DatacenterInfo("ap-northeast-1", "AWS", "Tokyo", "Tokyo", "JP", "Asia Pacific", "NRT"),
        ]
        
        providers["aws"] = HostingProvider(
            name="Amazon Web Services",
            type="cloud",
            asn_ranges=["AS16509", "AS14618"],
            ip_ranges=["********/8", "********/8", "*******/8"],
            datacenters=aws_datacenters,
            website="https://aws.amazon.com"
        )
        
        # Google Cloud
        gcp_datacenters = [
            DatacenterInfo("us-central1", "GCP", "Iowa", "Council Bluffs", "US", "North America"),
            DatacenterInfo("us-east1", "GCP", "S. Carolina", "Moncks Corner", "US", "North America"),
            DatacenterInfo("europe-west1", "GCP", "Belgium", "St. Ghislain", "BE", "Europe"),
            DatacenterInfo("asia-southeast1", "GCP", "Singapore", "Singapore", "SG", "Asia Pacific"),
        ]
        
        providers["gcp"] = HostingProvider(
            name="Google Cloud Platform",
            type="cloud",
            asn_ranges=["AS15169", "AS36040"],
            ip_ranges=["********/8", "********/8", "***********/16"],
            datacenters=gcp_datacenters,
            website="https://cloud.google.com"
        )
        
        # Microsoft Azure
        azure_datacenters = [
            DatacenterInfo("East US", "Azure", "Virginia", "Boydton", "US", "North America"),
            DatacenterInfo("West US 2", "Azure", "Washington", "Quincy", "US", "North America"),
            DatacenterInfo("West Europe", "Azure", "Netherlands", "Amsterdam", "NL", "Europe"),
            DatacenterInfo("Southeast Asia", "Azure", "Singapore", "Singapore", "SG", "Asia Pacific"),
        ]
        
        providers["azure"] = HostingProvider(
            name="Microsoft Azure",
            type="cloud",
            asn_ranges=["AS8075", "AS8068"],
            ip_ranges=["1*******/8", "40.0.0.0/8", "********/8"],
            datacenters=azure_datacenters,
            website="https://azure.microsoft.com"
        )
        
        # Equinix
        equinix_datacenters = [
            DatacenterInfo("NY4", "Equinix", "New York", "Secaucus", "US", "North America", "NY4"),
            DatacenterInfo("LD5", "Equinix", "London", "Slough", "GB", "Europe", "LD5"),
            DatacenterInfo("TY3", "Equinix", "Tokyo", "Tokyo", "JP", "Asia Pacific", "TY3"),
            DatacenterInfo("SG1", "Equinix", "Singapore", "Singapore", "SG", "Asia Pacific", "SG1"),
        ]
        
        providers["equinix"] = HostingProvider(
            name="Equinix",
            type="colo",
            asn_ranges=["AS54825", "AS26415"],
            ip_ranges=[],
            datacenters=equinix_datacenters,
            website="https://www.equinix.com"
        )
        
        # Cloudflare
        providers["cloudflare"] = HostingProvider(
            name="Cloudflare",
            type="cdn",
            asn_ranges=["AS13335"],
            ip_ranges=["**********/12", "**********/13"],
            datacenters=[],
            website="https://www.cloudflare.com"
        )
        
        # DigitalOcean
        do_datacenters = [
            DatacenterInfo("NYC1", "DigitalOcean", "New York", "New York", "US", "North America"),
            DatacenterInfo("SFO3", "DigitalOcean", "San Francisco", "San Francisco", "US", "North America"),
            DatacenterInfo("LON1", "DigitalOcean", "London", "London", "GB", "Europe"),
            DatacenterInfo("SGP1", "DigitalOcean", "Singapore", "Singapore", "SG", "Asia Pacific"),
        ]
        
        providers["digitalocean"] = HostingProvider(
            name="DigitalOcean",
            type="cloud",
            asn_ranges=["AS14061"],
            ip_ranges=["***********/16", "***********/16"],
            datacenters=do_datacenters,
            website="https://www.digitalocean.com"
        )
        
        return providers
    
    def _build_asn_mapping(self) -> Dict[str, str]:
        """Build mapping from ASN to provider name."""
        mapping = {}
        for provider_key, provider in self.providers.items():
            for asn in provider.asn_ranges:
                mapping[asn] = provider_key
        return mapping
    
    def _load_custom_mapping(self, filename: str):
        """Load custom datacenter mapping from file."""
        try:
            with open(filename, 'r') as f:
                custom_data = json.load(f)
                # Process custom mapping data
                self.logger.info(f"Loaded custom mapping from {filename}")
        except FileNotFoundError:
            self.logger.warning(f"Custom mapping file not found: {filename}")
        except Exception as e:
            self.logger.error(f"Error loading custom mapping: {e}")
    
    def identify_provider_by_asn(self, asn: str) -> Optional[HostingProvider]:
        """Identify hosting provider by ASN."""
        if asn in self.asn_to_provider:
            provider_key = self.asn_to_provider[asn]
            return self.providers[provider_key]
        return None
    
    def identify_provider_by_ip(self, ip_address: str) -> Optional[HostingProvider]:
        """Identify hosting provider by IP address range."""
        import ipaddress
        
        try:
            ip = ipaddress.ip_address(ip_address)
            
            for provider in self.providers.values():
                for ip_range in provider.ip_ranges:
                    try:
                        network = ipaddress.ip_network(ip_range)
                        if ip in network:
                            return provider
                    except ValueError:
                        continue
        except ValueError:
            pass
        
        return None
    
    def identify_datacenter(self, whois_data: Dict[str, Any], 
                          location_hints: List[str] = None) -> Optional[DatacenterInfo]:
        """Identify specific datacenter from WHOIS data and location hints."""
        if location_hints is None:
            location_hints = []
        
        # First try to identify provider
        provider = None
        if 'asn' in whois_data and whois_data['asn']:
            provider = self.identify_provider_by_asn(whois_data['asn'])
        
        if not provider and 'ip_address' in whois_data:
            provider = self.identify_provider_by_ip(whois_data['ip_address'])
        
        if not provider:
            return None
        
        # Try to match specific datacenter
        country = (whois_data.get('country') or '').upper()
        city = (whois_data.get('city') or '').lower()
        
        for datacenter in provider.datacenters:
            # Match by country
            if datacenter.country == country:
                # Match by city if available
                if city and city in datacenter.city.lower():
                    return datacenter
                # Match by location hints
                for hint in location_hints:
                    if hint.lower() in datacenter.location.lower():
                        return datacenter
        
        # Return first datacenter in same country if no specific match
        for datacenter in provider.datacenters:
            if datacenter.country == country:
                return datacenter
        
        return None
    
    def get_connectivity_options(self, datacenter: DatacenterInfo) -> List[Dict[str, Any]]:
        """Get connectivity options for a specific datacenter."""
        options = []
        
        if datacenter.provider == "AWS":
            options.extend([
                {
                    "type": "EC2 Instance",
                    "description": f"EC2 instance in {datacenter.name}",
                    "latency_estimate": "< 1ms",
                    "cost_estimate": "$50-500/month",
                    "setup_time": "Minutes"
                },
                {
                    "type": "Direct Connect",
                    "description": f"Dedicated connection to {datacenter.name}",
                    "latency_estimate": "< 1ms",
                    "cost_estimate": "$500-5000/month",
                    "setup_time": "Weeks"
                }
            ])
        
        elif datacenter.provider == "Equinix":
            options.extend([
                {
                    "type": "Colocation",
                    "description": f"Rack space in {datacenter.facility_code}",
                    "latency_estimate": "< 0.5ms",
                    "cost_estimate": "$1000-10000/month",
                    "setup_time": "Weeks"
                },
                {
                    "type": "Cross Connect",
                    "description": f"Direct connection in {datacenter.facility_code}",
                    "latency_estimate": "< 0.1ms",
                    "cost_estimate": "$100-500/month",
                    "setup_time": "Days"
                }
            ])
        
        elif datacenter.provider in ["GCP", "Azure"]:
            options.extend([
                {
                    "type": "VM Instance",
                    "description": f"Virtual machine in {datacenter.name}",
                    "latency_estimate": "< 1ms",
                    "cost_estimate": "$50-500/month",
                    "setup_time": "Minutes"
                }
            ])
        
        elif datacenter.provider == "DigitalOcean":
            options.extend([
                {
                    "type": "Droplet",
                    "description": f"VPS in {datacenter.name}",
                    "latency_estimate": "< 2ms",
                    "cost_estimate": "$20-200/month",
                    "setup_time": "Minutes"
                }
            ])
        
        return options
    
    def analyze_hosting_infrastructure(self, network_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze hosting infrastructure from network analysis results."""
        result = {
            "provider": None,
            "datacenter": None,
            "connectivity_options": [],
            "confidence": "low"
        }
        
        # Analyze WHOIS results
        whois_results = network_analysis.get('whois_results', [])
        
        for whois_data in whois_results:
            if whois_data.get('error'):
                continue
            
            # Try to identify provider and datacenter
            provider = None
            if whois_data.get('asn'):
                provider = self.identify_provider_by_asn(whois_data['asn'])
            
            if not provider and whois_data.get('ip_address'):
                provider = self.identify_provider_by_ip(whois_data['ip_address'])
            
            if provider:
                result["provider"] = asdict(provider)
                result["confidence"] = "medium"
                
                # Try to identify specific datacenter
                datacenter = self.identify_datacenter(whois_data)
                if datacenter:
                    result["datacenter"] = asdict(datacenter)
                    result["connectivity_options"] = self.get_connectivity_options(datacenter)
                    result["confidence"] = "high"
                    break
        
        return result
