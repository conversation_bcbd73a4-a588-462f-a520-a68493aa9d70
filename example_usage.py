#!/usr/bin/env python3
"""
Example usage of the Cryptocurrency Exchange Connectivity Analyzer

This script demonstrates various ways to use the analyzer for different scenarios.
"""

import asyncio
import json
from pathlib import Path

from crypto_connectivity_analyzer import CryptoConnectivityAnalyzer

async def example_basic_analysis():
    """Example 1: Basic analysis of all exchanges."""
    print("Example 1: Basic Analysis of All Exchanges")
    print("=" * 50)
    
    # Create analyzer with default configuration
    analyzer = CryptoConnectivityAnalyzer()
    
    # Run analysis on all exchanges
    result = await analyzer.run_full_analysis()
    
    if result['success']:
        print("✓ Analysis completed successfully!")
        print(f"Generated reports: {list(result['report_files'].keys())}")
    else:
        print(f"✗ Analysis failed: {result['error']}")

async def example_specific_exchanges():
    """Example 2: Analyze specific exchanges only."""
    print("\nExample 2: Analyze Specific Exchanges")
    print("=" * 50)
    
    analyzer = CryptoConnectivityAnalyzer()
    
    # Analyze only major exchanges
    target_exchanges = ['binance', 'coinbase', 'kraken', 'okx']
    
    result = await analyzer.run_full_analysis(
        exchange_filter=target_exchanges
    )
    
    if result['success']:
        print(f"✓ Analyzed {len(target_exchanges)} exchanges")
        
        # Print summary of results
        for analysis in result['analysis_results']:
            if not analysis.get('error'):
                hostname = analysis.get('hostname', 'Unknown')
                dns_info = analysis.get('dns', {})
                ips = dns_info.get('ipv4_addresses', [])
                ip_str = ips[0] if ips else 'No IP'
                print(f"  {hostname}: {ip_str}")

async def example_custom_requirements():
    """Example 3: Analysis with custom latency and cost requirements."""
    print("\nExample 3: Custom Requirements Analysis")
    print("=" * 50)
    
    analyzer = CryptoConnectivityAnalyzer()
    
    # Define strict requirements for high-frequency trading
    hft_requirements = {
        'max_latency_ms': 2.0,  # Very low latency requirement
        'max_cost_usd': 5000,   # Higher budget for premium connectivity
        'preferred_providers': ['equinix', 'aws', 'gcp']
    }
    
    result = await analyzer.run_full_analysis(
        exchange_filter=['binance', 'coinbase'],  # Focus on major exchanges
        requirements=hft_requirements
    )
    
    if result['success']:
        print("✓ High-frequency trading analysis completed")
        
        # Show top recommendations
        recommendations = result['recommendations']
        if recommendations:
            print("\nTop HFT Recommendations:")
            for i, rec in enumerate(recommendations[:3], 1):
                print(f"{i}. {rec.exchange_name}")
                print(f"   Provider: {rec.provider}")
                print(f"   Latency: {rec.estimated_latency}")
                print(f"   Cost: {rec.monthly_cost_range}")
                print(f"   Confidence: {rec.confidence_score:.2f}")

async def example_single_exchange_deep_dive():
    """Example 4: Deep dive analysis of a single exchange."""
    print("\nExample 4: Single Exchange Deep Dive")
    print("=" * 50)
    
    analyzer = CryptoConnectivityAnalyzer()
    
    # Analyze just Binance in detail
    analysis_results = await analyzer.analyze_all_exchanges(['binance'])
    
    if analysis_results and not analysis_results[0].get('error'):
        result = analysis_results[0]
        
        print("Binance Connectivity Analysis:")
        print(f"URL: {result['url']}")
        print(f"Hostname: {result['hostname']}")
        
        # DNS information
        dns_info = result.get('dns', {})
        print(f"IPv4 Addresses: {', '.join(dns_info.get('ipv4_addresses', []))}")
        print(f"IPv6 Addresses: {', '.join(dns_info.get('ipv6_addresses', []))}")
        
        # Traceroute information
        traceroute_results = result.get('traceroute_results', [])
        if traceroute_results:
            tr = traceroute_results[0]
            if tr.get('success'):
                print(f"Traceroute Hops: {len(tr.get('hops', []))}")
                
                # Show first few hops
                for hop in tr.get('hops', [])[:3]:
                    hop_num = hop.get('hop_number', 0)
                    ip = hop.get('ip_address', 'Unknown')
                    avg_rtt = hop.get('avg_rtt_ms', 0)
                    print(f"  Hop {hop_num}: {ip} ({avg_rtt:.2f}ms)")
        
        # WHOIS information
        whois_results = result.get('whois_results', [])
        if whois_results:
            whois = whois_results[0]
            print(f"ASN: {whois.get('asn', 'Unknown')}")
            print(f"Organization: {whois.get('organization', 'Unknown')}")
            print(f"Country: {whois.get('country', 'Unknown')}")
        
        # Hosting infrastructure
        hosting_info = result.get('hosting_infrastructure', {})
        provider_info = hosting_info.get('provider', {})
        datacenter_info = hosting_info.get('datacenter', {})
        
        if provider_info:
            print(f"Hosting Provider: {provider_info.get('name', 'Unknown')}")
        if datacenter_info:
            print(f"Datacenter: {datacenter_info.get('name', 'Unknown')}")
            print(f"Location: {datacenter_info.get('location', 'Unknown')}")

def example_analyze_results():
    """Example 5: Analyze existing results from JSON file."""
    print("\nExample 5: Analyze Existing Results")
    print("=" * 50)
    
    # Look for existing results file
    results_dir = Path("results")
    if results_dir.exists():
        json_files = list(results_dir.glob("crypto_connectivity_analysis_*.json"))
        
        if json_files:
            latest_file = max(json_files, key=lambda p: p.stat().st_mtime)
            print(f"Loading results from: {latest_file}")
            
            with open(latest_file, 'r') as f:
                data = json.load(f)
            
            # Analyze the results
            analysis_results = data.get('analysis_results', [])
            recommendations = data.get('recommendations', [])
            
            print(f"Total exchanges analyzed: {len(analysis_results)}")
            print(f"Total recommendations: {len(recommendations)}")
            
            # Show provider distribution
            providers = {}
            for result in analysis_results:
                if not result.get('error'):
                    hosting_info = result.get('hosting_infrastructure', {})
                    provider_info = hosting_info.get('provider', {})
                    provider_name = provider_info.get('name', 'Unknown')
                    providers[provider_name] = providers.get(provider_name, 0) + 1
            
            print("\nProvider Distribution:")
            for provider, count in sorted(providers.items(), key=lambda x: x[1], reverse=True):
                print(f"  {provider}: {count} exchanges")
            
            # Show latency distribution
            latencies = []
            for result in analysis_results:
                if not result.get('error'):
                    traceroute_results = result.get('traceroute_results', [])
                    for tr in traceroute_results:
                        if tr.get('success') and tr.get('hops'):
                            for hop in tr['hops']:
                                if hop.get('rtt_ms'):
                                    latencies.extend(hop['rtt_ms'])
            
            if latencies:
                avg_latency = sum(latencies) / len(latencies)
                min_latency = min(latencies)
                max_latency = max(latencies)
                
                print(f"\nLatency Statistics:")
                print(f"  Average: {avg_latency:.2f}ms")
                print(f"  Minimum: {min_latency:.2f}ms")
                print(f"  Maximum: {max_latency:.2f}ms")
        else:
            print("No existing results files found. Run an analysis first.")
    else:
        print("Results directory not found. Run an analysis first.")

async def example_cost_optimization():
    """Example 6: Cost-optimized analysis for budget-conscious users."""
    print("\nExample 6: Cost-Optimized Analysis")
    print("=" * 50)
    
    analyzer = CryptoConnectivityAnalyzer()
    
    # Define budget-friendly requirements
    budget_requirements = {
        'max_latency_ms': 20.0,  # More relaxed latency
        'max_cost_usd': 100,     # Low budget
        'preferred_providers': ['digitalocean', 'vultr', 'aws']
    }
    
    result = await analyzer.run_full_analysis(
        exchange_filter=['binance', 'coinbase', 'kraken'],
        requirements=budget_requirements
    )
    
    if result['success']:
        print("✓ Budget-friendly analysis completed")
        
        recommendations = result['recommendations']
        if recommendations:
            print("\nBudget-Friendly Recommendations:")
            for rec in recommendations:
                # Extract cost estimate
                cost_str = rec.monthly_cost_range
                print(f"• {rec.exchange_name}")
                print(f"  Connection: {rec.connection_type}")
                print(f"  Cost: {cost_str}")
                print(f"  Latency: {rec.estimated_latency}")
                print(f"  Setup: {rec.setup_time}")

async def main():
    """Run all examples."""
    print("Cryptocurrency Exchange Connectivity Analyzer - Examples")
    print("=" * 60)
    
    examples = [
        example_basic_analysis,
        example_specific_exchanges,
        example_custom_requirements,
        example_single_exchange_deep_dive,
        example_analyze_results,  # This one is synchronous
        example_cost_optimization
    ]
    
    for i, example_func in enumerate(examples, 1):
        try:
            if asyncio.iscoroutinefunction(example_func):
                await example_func()
            else:
                example_func()
        except Exception as e:
            print(f"Example {i} failed: {e}")
        
        if i < len(examples):
            input("\nPress Enter to continue to the next example...")

if __name__ == "__main__":
    asyncio.run(main())
