"""
Cryptocurrency Exchange Websocket Endpoints Database
Contains comprehensive list of major exchange websocket endpoints for market data.
"""

import json
from typing import Dict, List, Optional
from dataclasses import dataclass

@dataclass
class ExchangeEndpoint:
    """Represents a cryptocurrency exchange websocket endpoint."""
    name: str
    url: str
    data_types: List[str]
    order_book_type: str  # L1, L2, L3, MBO (Market-by-Order)
    region: Optional[str] = None
    description: Optional[str] = None
    requires_auth: bool = False

class ExchangeEndpoints:
    """Database of cryptocurrency exchange websocket endpoints."""
    
    def __init__(self):
        self.endpoints = self._load_endpoints()
    
    def _load_endpoints(self) -> List[ExchangeEndpoint]:
        """Load the comprehensive list of exchange endpoints."""
        return [
            # Binance
            ExchangeEndpoint(
                name="binance",
                url="wss://stream.binance.com:9443/ws",
                data_types=["ticker", "trades", "orderbook", "klines"],
                order_book_type="L2",
                region="global",
                description="Binance main websocket stream"
            ),
            ExchangeEndpoint(
                name="binance_futures",
                url="wss://fstream.binance.com/ws",
                data_types=["ticker", "trades", "orderbook", "klines", "funding"],
                order_book_type="L2",
                region="global",
                description="Binance Futures websocket stream"
            ),
            ExchangeEndpoint(
                name="binance_us",
                url="wss://stream.binance.us:9443/ws",
                data_types=["ticker", "trades", "orderbook", "klines"],
                order_book_type="L2",
                region="us",
                description="Binance US websocket stream"
            ),
            
            # Coinbase
            ExchangeEndpoint(
                name="coinbase",
                url="wss://ws-feed.exchange.coinbase.com",
                data_types=["ticker", "trades", "orderbook", "matches"],
                order_book_type="L3",
                region="global",
                description="Coinbase Advanced Trade websocket feed"
            ),
            
            # Kraken
            ExchangeEndpoint(
                name="kraken",
                url="wss://ws.kraken.com",
                data_types=["ticker", "trades", "orderbook", "ohlc"],
                order_book_type="L2",
                region="global",
                description="Kraken main websocket feed"
            ),
            ExchangeEndpoint(
                name="kraken_futures",
                url="wss://futures.kraken.com/ws/v1",
                data_types=["ticker", "trades", "orderbook", "funding"],
                order_book_type="L2",
                region="global",
                description="Kraken Futures websocket feed"
            ),
            
            # OKX (formerly OKEx)
            ExchangeEndpoint(
                name="okx",
                url="wss://ws.okx.com:8443/ws/v5/public",
                data_types=["ticker", "trades", "orderbook", "klines", "funding"],
                order_book_type="L2",
                region="global",
                description="OKX public websocket feed"
            ),
            
            # Huobi
            ExchangeEndpoint(
                name="huobi",
                url="wss://api.huobi.pro/ws",
                data_types=["ticker", "trades", "orderbook", "klines"],
                order_book_type="L2",
                region="global",
                description="Huobi Global websocket feed"
            ),
            ExchangeEndpoint(
                name="huobi_futures",
                url="wss://api.hbdm.com/ws",
                data_types=["ticker", "trades", "orderbook", "klines", "funding"],
                order_book_type="L2",
                region="global",
                description="Huobi Futures websocket feed"
            ),
            
            # Bybit
            ExchangeEndpoint(
                name="bybit",
                url="wss://stream.bybit.com/v5/public/spot",
                data_types=["ticker", "trades", "orderbook", "klines"],
                order_book_type="L2",
                region="global",
                description="Bybit Spot websocket feed"
            ),
            ExchangeEndpoint(
                name="bybit_futures",
                url="wss://stream.bybit.com/v5/public/linear",
                data_types=["ticker", "trades", "orderbook", "klines", "funding"],
                order_book_type="L2",
                region="global",
                description="Bybit Linear Futures websocket feed"
            ),
            
            # KuCoin
            ExchangeEndpoint(
                name="kucoin",
                url="wss://ws-api.kucoin.com/endpoint",
                data_types=["ticker", "trades", "orderbook", "klines"],
                order_book_type="L2",
                region="global",
                description="KuCoin websocket feed (requires token)"
            ),
            
            # Gate.io
            ExchangeEndpoint(
                name="gateio",
                url="wss://api.gateio.ws/ws/v4/",
                data_types=["ticker", "trades", "orderbook", "klines"],
                order_book_type="L2",
                region="global",
                description="Gate.io websocket feed"
            ),
            
            # Bitfinex
            ExchangeEndpoint(
                name="bitfinex",
                url="wss://api-pub.bitfinex.com/ws/2",
                data_types=["ticker", "trades", "orderbook", "candles", "funding"],
                order_book_type="L2",
                region="global",
                description="Bitfinex public websocket feed"
            ),
            
            # Bitstamp
            ExchangeEndpoint(
                name="bitstamp",
                url="wss://ws.bitstamp.net",
                data_types=["ticker", "trades", "orderbook"],
                order_book_type="L2",
                region="europe",
                description="Bitstamp websocket feed"
            ),
            
            # Gemini
            ExchangeEndpoint(
                name="gemini",
                url="wss://api.gemini.com/v1/marketdata",
                data_types=["ticker", "trades", "orderbook"],
                order_book_type="L2",
                region="us",
                description="Gemini market data websocket"
            ),
            
            # Deribit
            ExchangeEndpoint(
                name="deribit",
                url="wss://www.deribit.com/ws/api/v2",
                data_types=["ticker", "trades", "orderbook", "funding"],
                order_book_type="L2",
                region="global",
                description="Deribit websocket feed"
            ),
            
            # BitMEX
            ExchangeEndpoint(
                name="bitmex",
                url="wss://www.bitmex.com/realtime",
                data_types=["ticker", "trades", "orderbook", "funding"],
                order_book_type="L2",
                region="global",
                description="BitMEX real-time websocket feed"
            ),
            
            # Crypto.com
            ExchangeEndpoint(
                name="crypto_com",
                url="wss://stream.crypto.com/v2/market",
                data_types=["ticker", "trades", "orderbook"],
                order_book_type="L2",
                region="global",
                description="Crypto.com market websocket feed"
            ),
            
            # FTX (Historical - now closed)
            ExchangeEndpoint(
                name="ftx",
                url="wss://ftx.com/ws/",
                data_types=["ticker", "trades", "orderbook", "funding"],
                order_book_type="L2",
                region="global",
                description="FTX websocket feed (historical)"
            ),
        ]
    
    def get_all_endpoints(self) -> List[ExchangeEndpoint]:
        """Get all exchange endpoints."""
        return self.endpoints
    
    def get_by_name(self, name: str) -> Optional[ExchangeEndpoint]:
        """Get endpoint by exchange name."""
        for endpoint in self.endpoints:
            if endpoint.name.lower() == name.lower():
                return endpoint
        return None
    
    def get_by_region(self, region: str) -> List[ExchangeEndpoint]:
        """Get endpoints by region."""
        return [ep for ep in self.endpoints if ep.region == region]
    
    def get_mbo_endpoints(self) -> List[ExchangeEndpoint]:
        """Get endpoints that support Market-by-Order data."""
        return [ep for ep in self.endpoints if ep.order_book_type == "MBO"]
    
    def get_l3_endpoints(self) -> List[ExchangeEndpoint]:
        """Get endpoints that support Level 3 order book data."""
        return [ep for ep in self.endpoints if ep.order_book_type == "L3"]
    
    def to_dict(self) -> Dict:
        """Convert endpoints to dictionary format."""
        return {
            "endpoints": [
                {
                    "name": ep.name,
                    "url": ep.url,
                    "data_types": ep.data_types,
                    "order_book_type": ep.order_book_type,
                    "region": ep.region,
                    "description": ep.description,
                    "requires_auth": ep.requires_auth
                }
                for ep in self.endpoints
            ]
        }
    
    def save_to_file(self, filename: str):
        """Save endpoints to JSON file."""
        with open(filename, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)

if __name__ == "__main__":
    # Example usage
    endpoints = ExchangeEndpoints()
    print(f"Loaded {len(endpoints.get_all_endpoints())} exchange endpoints")
    
    # Show MBO endpoints
    mbo_endpoints = endpoints.get_mbo_endpoints()
    print(f"Market-by-Order endpoints: {len(mbo_endpoints)}")
    
    # Show L3 endpoints
    l3_endpoints = endpoints.get_l3_endpoints()
    print(f"Level 3 order book endpoints: {len(l3_endpoints)}")
    for ep in l3_endpoints:
        print(f"  - {ep.name}: {ep.url}")
