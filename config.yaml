# Crypto Exchange Connectivity Analyzer Configuration

# Network Analysis Settings
network:
  timeout: 30  # seconds
  max_retries: 3
  concurrent_limit: 10
  traceroute_max_hops: 30
  ping_count: 5

# Output Settings
output:
  formats: ["json", "csv", "html"]
  output_dir: "results"
  timestamp_format: "%Y%m%d_%H%M%S"
  
# Logging Settings
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: "crypto_analyzer.log"
  max_size_mb: 10
  backup_count: 5

# Exchange Settings
exchanges:
  # Priority exchanges for analysis
  priority:
    - "binance"
    - "coinbase"
    - "kraken"
    - "ftx"
    - "okx"
    - "huobi"
    - "bybit"
    - "kucoin"
  
  # Include all available exchanges
  include_all: true

# Datacenter Database Settings
datacenter_db:
  # Path to custom datacenter mapping file
  custom_mapping: "datacenter_mapping.json"
  
  # Enable automatic datacenter detection
  auto_detect: true

# Connectivity Analysis
connectivity:
  # VPS providers to analyze
  vps_providers:
    - "aws"
    - "gcp"
    - "azure"
    - "digitalocean"
    - "vultr"
    - "linode"
  
  # Colocation providers
  colo_providers:
    - "equinix"
    - "digitalrealty"
    - "cyxtera"
    - "coresite"
  
  # Include cost estimates
  include_costs: true
  
  # Currency for cost estimates
  currency: "USD"
