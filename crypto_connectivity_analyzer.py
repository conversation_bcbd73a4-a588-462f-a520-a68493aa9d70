#!/usr/bin/env python3
"""
Cryptocurrency Exchange Market Data Connectivity Analyzer

This program analyzes connectivity to cryptocurrency exchange market data websocket endpoints,
providing comprehensive network analysis, datacenter identification, and connectivity recommendations.

Usage:
    python crypto_connectivity_analyzer.py [options]

Features:
- DNS resolution and IP address mapping
- Network route analysis with traceroute/MTR
- WHOIS lookups for network ownership
- Datacenter and hosting provider identification
- Connectivity recommendations with cost estimates
- Multiple output formats (JSON, CSV, HTML)
"""

import asyncio
import logging
import sys
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional
import yaml

# Import our modules
from exchange_endpoints import ExchangeEndpoints
from network_analyzer import NetworkAnalyzer
from datacenter_mapper import DatacenterMapper
from connectivity_recommender import ConnectivityRecommender
from report_generator import ReportGenerator

class CryptoConnectivityAnalyzer:
    """Main analyzer class that orchestrates the analysis process."""
    
    def __init__(self, config_file: str = "config.yaml"):
        self.config = self._load_config(config_file)
        self._setup_logging()
        
        # Initialize components
        self.endpoints = ExchangeEndpoints()
        self.network_analyzer = NetworkAnalyzer(
            timeout=self.config['network']['timeout'],
            max_retries=self.config['network']['max_retries']
        )
        self.datacenter_mapper = DatacenterMapper(
            custom_mapping_file=self.config['datacenter_db'].get('custom_mapping')
        )
        self.connectivity_recommender = ConnectivityRecommender()
        self.report_generator = ReportGenerator(
            output_dir=self.config['output']['output_dir']
        )
        
        self.logger = logging.getLogger(__name__)
    
    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(config_file, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            print(f"Warning: Config file {config_file} not found. Using defaults.")
            return self._get_default_config()
        except Exception as e:
            print(f"Error loading config: {e}. Using defaults.")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration."""
        return {
            'network': {
                'timeout': 30,
                'max_retries': 3,
                'concurrent_limit': 10,
                'traceroute_max_hops': 30,
                'ping_count': 5
            },
            'output': {
                'formats': ['json', 'csv', 'html'],
                'output_dir': 'results',
                'timestamp_format': '%Y%m%d_%H%M%S'
            },
            'logging': {
                'level': 'INFO',
                'file': 'crypto_analyzer.log',
                'max_size_mb': 10,
                'backup_count': 5
            },
            'exchanges': {
                'priority': ['binance', 'coinbase', 'kraken', 'okx', 'huobi', 'bybit'],
                'include_all': True
            },
            'datacenter_db': {
                'custom_mapping': None,
                'auto_detect': True
            },
            'connectivity': {
                'vps_providers': ['aws', 'gcp', 'azure', 'digitalocean'],
                'colo_providers': ['equinix', 'digitalrealty'],
                'include_costs': True,
                'currency': 'USD'
            }
        }
    
    def _setup_logging(self):
        """Setup logging configuration."""
        log_level = getattr(logging, self.config['logging']['level'].upper())
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # Configure root logger
        logging.basicConfig(
            level=log_level,
            format=log_format,
            handlers=[
                logging.FileHandler(self.config['logging']['file']),
                logging.StreamHandler(sys.stdout)
            ]
        )
    
    async def analyze_all_exchanges(self, 
                                  exchange_filter: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Analyze all exchange endpoints or filtered subset."""
        endpoints = self.endpoints.get_all_endpoints()
        
        # Filter exchanges if specified
        if exchange_filter:
            endpoints = [ep for ep in endpoints if ep.name in exchange_filter]
        
        # Extract URLs for analysis
        urls = [ep.url for ep in endpoints]
        
        self.logger.info(f"Starting analysis of {len(urls)} exchange endpoints")
        
        # Perform network analysis
        concurrent_limit = self.config['network']['concurrent_limit']
        analysis_results = await self.network_analyzer.batch_analyze(urls, concurrent_limit)
        
        # Enhance results with hosting infrastructure analysis
        for result in analysis_results:
            if not result.get('error'):
                hosting_info = self.datacenter_mapper.analyze_hosting_infrastructure(result)
                result['hosting_infrastructure'] = hosting_info
        
        self.logger.info(f"Completed analysis of {len(analysis_results)} endpoints")
        return analysis_results
    
    def generate_recommendations(self, analysis_results: List[Dict[str, Any]], 
                               requirements: Optional[Dict[str, Any]] = None) -> List[Any]:
        """Generate connectivity recommendations based on analysis results."""
        if requirements is None:
            requirements = {
                'max_latency_ms': 10,
                'max_cost_usd': 1000,
                'preferred_providers': ['aws', 'gcp', 'equinix']
            }
        
        self.logger.info("Generating connectivity recommendations")
        recommendations = self.connectivity_recommender.generate_recommendations(
            analysis_results, requirements
        )
        
        self.logger.info(f"Generated {len(recommendations)} recommendations")
        return recommendations
    
    def generate_reports(self, analysis_results: List[Dict[str, Any]], 
                        recommendations: List[Any]) -> Dict[str, str]:
        """Generate all output reports."""
        self.logger.info("Generating reports")
        
        report_files = self.report_generator.generate_all_reports(
            analysis_results, recommendations, self.config
        )
        
        self.logger.info(f"Generated {len(report_files)} report files")
        for format_type, filename in report_files.items():
            self.logger.info(f"  {format_type.upper()}: {filename}")
        
        return report_files
    
    async def run_full_analysis(self, 
                              exchange_filter: Optional[List[str]] = None,
                              requirements: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Run complete analysis pipeline."""
        try:
            self.logger.info("Starting cryptocurrency exchange connectivity analysis")
            
            # Step 1: Analyze all exchanges
            analysis_results = await self.analyze_all_exchanges(exchange_filter)
            
            # Step 2: Generate recommendations
            recommendations = self.generate_recommendations(analysis_results, requirements)
            
            # Step 3: Generate reports
            report_files = self.generate_reports(analysis_results, recommendations)
            
            # Step 4: Print summary
            self._print_summary(analysis_results, recommendations, report_files)
            
            return {
                'analysis_results': analysis_results,
                'recommendations': recommendations,
                'report_files': report_files,
                'success': True
            }
            
        except Exception as e:
            self.logger.error(f"Analysis failed: {e}", exc_info=True)
            return {
                'error': str(e),
                'success': False
            }
    
    def _print_summary(self, analysis_results: List[Dict[str, Any]], 
                      recommendations: List[Any], 
                      report_files: Dict[str, str]):
        """Print analysis summary to console."""
        print("\n" + "="*60)
        print("CRYPTOCURRENCY EXCHANGE CONNECTIVITY ANALYSIS COMPLETE")
        print("="*60)
        
        # Analysis summary
        total_exchanges = len(analysis_results)
        successful = len([r for r in analysis_results if not r.get('error')])
        failed = total_exchanges - successful
        
        print(f"\nAnalysis Summary:")
        print(f"  Total Exchanges: {total_exchanges}")
        print(f"  Successful: {successful}")
        print(f"  Failed: {failed}")
        
        # Top recommendations
        if recommendations:
            print(f"\nTop Connectivity Recommendations:")
            sorted_recs = sorted(recommendations, 
                               key=lambda x: x.confidence_score, 
                               reverse=True)
            
            for i, rec in enumerate(sorted_recs[:3], 1):
                print(f"  {i}. {rec.exchange_name}")
                print(f"     Provider: {rec.provider}")
                print(f"     Connection: {rec.connection_type}")
                print(f"     Latency: {rec.estimated_latency}")
                print(f"     Cost: {rec.monthly_cost_range}")
                print(f"     Confidence: {rec.confidence_score:.2f}")
        
        # Report files
        print(f"\nGenerated Reports:")
        for format_type, filename in report_files.items():
            print(f"  {format_type.upper()}: {filename}")
        
        print("\n" + "="*60)

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Cryptocurrency Exchange Connectivity Analyzer",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Analyze all exchanges
  python crypto_connectivity_analyzer.py
  
  # Analyze specific exchanges
  python crypto_connectivity_analyzer.py --exchanges binance coinbase kraken
  
  # Use custom config file
  python crypto_connectivity_analyzer.py --config my_config.yaml
  
  # Set custom requirements
  python crypto_connectivity_analyzer.py --max-latency 5 --max-cost 500
        """
    )
    
    parser.add_argument(
        '--config', '-c',
        default='config.yaml',
        help='Configuration file path (default: config.yaml)'
    )
    
    parser.add_argument(
        '--exchanges', '-e',
        nargs='+',
        help='Specific exchanges to analyze (default: all)'
    )
    
    parser.add_argument(
        '--max-latency',
        type=float,
        default=10.0,
        help='Maximum acceptable latency in ms (default: 10.0)'
    )
    
    parser.add_argument(
        '--max-cost',
        type=float,
        default=1000.0,
        help='Maximum acceptable monthly cost in USD (default: 1000.0)'
    )
    
    parser.add_argument(
        '--output-dir', '-o',
        default='results',
        help='Output directory for reports (default: results)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Create analyzer
    analyzer = CryptoConnectivityAnalyzer(args.config)
    
    # Override config with command line arguments
    if args.output_dir != 'results':
        analyzer.config['output']['output_dir'] = args.output_dir
    
    if args.verbose:
        analyzer.config['logging']['level'] = 'DEBUG'
        analyzer._setup_logging()
    
    # Set requirements
    requirements = {
        'max_latency_ms': args.max_latency,
        'max_cost_usd': args.max_cost
    }
    
    # Run analysis
    try:
        result = asyncio.run(analyzer.run_full_analysis(
            exchange_filter=args.exchanges,
            requirements=requirements
        ))
        
        if result['success']:
            print("\nAnalysis completed successfully!")
            sys.exit(0)
        else:
            print(f"\nAnalysis failed: {result['error']}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\nAnalysis interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
